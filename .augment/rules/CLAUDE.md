---
description: "CLAUDE.md - 项目核心开发指南和架构规范"
globs: ["**/*"]
alwaysApply: true
---

# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.
注意回复我的时候，以及给代码写注释的时候还是要用中文
## Project Overview

This is a production-grade Traditional Chinese Medicine (TCM) application built with Django 4.1 and Django Ninja framework. The system serves as a comprehensive TCM platform with user management, questionnaire systems, prognosis/therapy recommendations, symptom tracking, content management, and payment processing.

## Essential Commands

### Development Environment Setup
```bash
# Enter virtual environment (REQUIRED for all Python commands)
start

# Run development server
start && python manage.py runserver

# Database migrations
start && python manage.py makemigrations
start && python manage.py migrate

# Check for errors
start && python manage.py check

# View recent error logs
tail -50 logs/error.log
```

### Docker Operations
```bash
# Build and run production environment
docker-compose up --build

# Build gray environment (staging)
docker-compose -f docker-compose.gray.yml up --build

# View container logs
docker logs demo_api-web-1
```

### Database Management
```bash
# Interactive database shell
start && python manage.py dbshell

# Import therapy data
start && python import_therapy_data_fixed.py

# Check database status
start && python check_database_status.py

# Backup database
start && python manage.py backup_mysql
```

## Critical Architecture Guidelines

### Async Programming (MANDATORY)
- **ALL API views MUST use `async def`** - This is strictly enforced
- **Database operations MUST use async_utils.py functions**: `get_async()`, `filter_async()`, `save_async()`
- **Connection pool management is CRITICAL** - Every database operation must properly close connections to prevent leaks
- **Use API_TIMER decorator** for performance monitoring

### Django Ninja API Organization
The project uses a sophisticated Django Ninja setup with **strict anti-circular-import protection**:

- **API Structure**: `api/ninja_apis/__init__.py` manages all API instances using singleton pattern
- **Router Registration**: All routers are registered exactly once to prevent "Router@'' has already been attached" errors
- **Module Organization**: Each business domain has its own API module (questionnaire, prognosis, auth, etc.)
- **No Direct NinjaAPI Creation**: Always use Router in modules, never create NinjaAPI instances directly

### Database Connection Pool Configuration
```python
# Uses dj_db_conn_pool with optimized settings:
'POOL_SIZE': 20,        # Base connections  
'MAX_OVERFLOW': 15,     # Additional connections (total 35)
'TIMEOUT': 60,          # Connection acquisition timeout
'RECYCLE': 1800,        # 30-minute connection recycling
'PRE_PING': True,       # Connection health checks
```

### User Authentication
```python
# Get user ID in any API endpoint:
user_id = request.user_id
```

## Key Development Patterns

### Async Database Operations
```python
from api.utils.async_utils import get_async, filter_async, save_async

# CORRECT - Use async utilities
user = await get_async(UserInfo, id=user_id)
symptoms = await filter_async(UserSymptom, user_id=user_id)

# INCORRECT - Never use sync operations in async views
user = UserInfo.objects.get(id=user_id)  # DON'T DO THIS
```

### API Performance Requirements
- Every API endpoint must include performance timing prints
- GET requests MUST implement caching mechanisms  
- Use pagination for datasets >100 items
- Implement proper rate limiting with django-ratelimit

### Error Handling & Logging
- All errors are captured by `ExceptionLoggingMiddleware`
- Critical errors logged to `logs/error.log` with rotation
- Use structured logging with proper log levels
- Never expose sensitive data in error responses

## Model Architecture

The system uses a modular model structure organized by business domain:

### Core Models
- **User Models**: `UserInfo`, `UserShareLog` - User management and activity tracking
- **Activity Models**: `Activity`, `UserActivity`, `UserCard` - Goal tracking and user engagement
- **Questionnaire Models**: `Questionnaire`, `TCMQuestion`, `DailyTCMQuizRecord` - Assessment system
- **Prognosis Models**: `PrognosisUserTherapy`, `PrognosisSymptom` - TCM recommendations
- **Payment Models**: `Order`, `PaymentOrder_Wechat` - WeChat Pay integration

### Database Design Principles
- Use `ForeignKey` with `related_name`, avoid hard deletes (use `SET_NULL`)
- All timestamp fields use `auto_now_add` and `auto_now` with timezone support
- Add `db_index=True` for frequently queried fields
- Implement `unique_together` constraints for business logic integrity

## Security & Production Considerations

### Connection Pool Management
**CRITICAL**: Database connection leaks are a severe production issue. The system automatically closes connections after each operation, but developers must:
- Never bypass async_utils.py functions
- Always await database operations
- Monitor connection pool metrics
- Test under load before deployment

### Rate Limiting & Security
- Django-ratelimit protects all public endpoints
- Redis-based session management with secure passwords
- CSRF protection disabled for API endpoints (token-based auth instead)
- Comprehensive logging for security monitoring

### Docker Production Setup
- Multi-service architecture: Django, MySQL, Redis, Nginx, Celery
- Automated health checks for all services
- Log rotation and volume management
- Security hardening with non-root users

## Common Pitfalls to Avoid

1. **NEVER create new ninja routes without following the established pattern** - This breaks the circular import protection
2. **NEVER use sync database operations in async views** - This causes connection pool issues
3. **NEVER bypass the virtual environment setup** - Always use `start &&` prefix for Python commands
4. **NEVER ignore connection pool warnings** - Connection leaks cause production outages
5. **NEVER skip performance monitoring** - All APIs must have timing and caching

## Testing & Debugging

### API Testing Scripts
- `test_api_stress.py` - Load testing for endpoints
- `test_connection_stability.py` - Database connection pool testing
- `test_prognosis_api.py` - Prognosis system functionality
- `simple_api_test.py` - Basic API health checks

### Performance Monitoring
- Built-in API_TIMER decorator for response time tracking
- Redis caching with automatic expiration
- Database query optimization with select_related/prefetch_related
- Elasticsearch integration for content search

## Integration Points

### External Services
- **WeChat Pay V3**: Complete payment processing with certificates
- **Huawei Cloud**: Content moderation and security
- **Redis**: Caching, sessions, and Celery message broker
- **Elasticsearch**: Content search with Chinese text analysis
- **MySQL 8.0**: Primary database with connection pooling

### API Endpoints Structure
- `/api/bank_api/` - Core banking/payment functionality
- `/api/doubao_aichat/` - AI chat and consultation
- `/api/questionnaire_api/` - Assessment and questionnaire system
- `/api/prognosis_api/` - TCM therapy recommendations (via routertest1)
- `/api/db_health_api/` - Database health monitoring

This architecture supports a high-traffic TCM application with robust error handling, performance optimization, and production-ready deployment patterns.
