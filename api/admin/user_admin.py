"""
用户相关模型的Admin配置
包括UserInfo、BannedToken和BannedAccount的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.contrib import messages
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.db import models
import json
from datetime import timedelta

from ..models import UserInfo
from ..models.user_models import BannedToken, BannedAccount


@admin.register(UserInfo)
class UserInfoAdmin(admin.ModelAdmin):
    # 定义在列表页显示的字段
    list_display = ('id', 'nickname', 'wx_phone_new', 'wx_openid_new', 'is_member', 'is_banned', 'created_at', 'last_login_date')
    
    # 定义可以搜索的字段
    search_fields = ('nickname', 'wx_phone_new', 'wx_openid_new')
    
    # 定义可以过滤的字段
    list_filter = ('is_member', 'is_banned', 'created_at', 'last_login_date')
    
    # 定义每页显示的记录数
    list_per_page = 50
    
    # 定义可以编辑的字段
    list_editable = ('is_member',)
    
    # 定义默认排序字段
    ordering = ('-created_at',)
    
    # 添加自定义视图
    change_list_template = 'admin/userinfo_change_list.html'
    
    # 添加解除封禁操作
    actions = ['unban_users']
    
    def unban_users(self, request, queryset):
        # 解除所选用户的封禁
        updated = queryset.filter(is_banned=True).update(
            is_banned=False,
            ban_reason=None,
            ban_until=None
        )
        
        if updated:
            # 同时删除关联的token封禁和账号封禁记录
            for user in queryset.filter(is_banned=False):
                try:
                    # 删除与用户相关的所有BannedToken记录
                    from ..models.user_models import BannedToken, BannedAccount
                    # 删除直接关联到用户的token
                    tokens_deleted = BannedToken.objects.filter(user=user).delete()[0]
                    
                    # 删除与用户手机号和微信ID相关的token
                    if user.wx_phone_new:
                        BannedToken.objects.filter(related_phone=user.wx_phone_new).delete()
                    if user.wx_openid_new:
                        BannedToken.objects.filter(related_openid=user.wx_openid_new).delete()
                    
                    # 删除与用户相关的BannedAccount记录
                    accounts_deleted = 0
                    query = models.Q()
                    if user.wx_phone_new:
                        query |= models.Q(phone_number=user.wx_phone_new)
                    if user.wx_openid_new:
                        query |= models.Q(wx_openid=user.wx_openid_new)
                    if user.wx_unionid_new:
                        query |= models.Q(wx_unionid=user.wx_unionid_new)
                    query |= models.Q(user_id=user.id)
                    
                    if query:
                        accounts_deleted = BannedAccount.objects.filter(query).delete()[0]
                    
                    print(f"用户 {user.id} 解除封禁: 删除了 {tokens_deleted} 个Token记录和 {accounts_deleted} 个账号封禁记录")
                except Exception as e:
                    print(f"解除用户 {user.id} 的封禁记录删除失败: {e}")
            
            messages.success(request, f"成功解除 {updated} 个用户的封禁状态，并彻底删除了相关的封禁记录")
        else:
            messages.info(request, "所选用户中没有被封禁的用户")
    
    unban_users.short_description = "解除选中用户的封禁"
    
    def changelist_view(self, request, extra_context=None):
        # 获取过去60天的统计数据（从30天改为60天）
        sixty_days_ago = timezone.now() - timedelta(days=60)
        
        # 日活统计 - 基于last_login_date字段
        daily_active_users = UserInfo.objects.filter(
            last_login_date__gte=sixty_days_ago
        ).annotate(
            login_date=models.functions.TruncDate('last_login_date')
        ).values('login_date').annotate(
            count=models.Count('id')
        ).order_by('login_date')
        
        # 新增用户统计
        new_users = UserInfo.objects.filter(
            created_at__gte=sixty_days_ago
        ).annotate(
            register_date=models.functions.TruncDate('created_at')
        ).values('register_date').annotate(
            count=models.Count('id')
        ).order_by('register_date')
        
        # 转化为图表数据格式
        dau_data = [
            {'date': item['login_date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in daily_active_users
        ]
        
        new_user_data = [
            {'date': item['register_date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in new_users
        ]
        
        # 计算每周活跃用户数（WAU）
        weeks_data = []
        current_date = timezone.now().date()
        for i in range(8):  # 显示8周数据
            start_date = current_date - timedelta(days=current_date.weekday(), weeks=i)
            end_date = start_date + timedelta(days=6)
            count = UserInfo.objects.filter(
                last_login_date__date__gte=start_date,
                last_login_date__date__lte=end_date
            ).count()
            weeks_data.append({
                'week': f'{start_date.strftime("%m.%d")}-{end_date.strftime("%m.%d")}',
                'count': count
            })
        weeks_data.reverse()  # 按时间顺序排列
        
        # 总用户数
        total_users = UserInfo.objects.count()
        # 会员用户数
        member_users = UserInfo.objects.filter(is_member=True).count()
        # 最近60天活跃用户
        active_users_60d = UserInfo.objects.filter(last_login_date__gte=sixty_days_ago).count()
        # 最近7天活跃用户
        seven_days_ago = timezone.now() - timedelta(days=7)
        active_users_7d = UserInfo.objects.filter(last_login_date__gte=seven_days_ago).count()
        # 今日活跃用户
        today = timezone.now().date()
        today_active_users = UserInfo.objects.filter(
            last_login_date__date=today
        ).count()
        # 昨日活跃用户
        yesterday = today - timedelta(days=1)
        yesterday_active_users = UserInfo.objects.filter(
            last_login_date__date=yesterday
        ).count()
        
        # 准备额外的上下文数据
        if extra_context is None:
            extra_context = {}
            
        extra_context.update({
            'dau_data': mark_safe(json.dumps(dau_data)),
            'new_user_data': mark_safe(json.dumps(new_user_data)),
            'weeks_data': mark_safe(json.dumps(weeks_data)),
            'total_users': total_users,
            'member_users': member_users,
            'active_users_60d': active_users_60d,
            'active_users_7d': active_users_7d,
            'today_active_users': today_active_users,
            'yesterday_active_users': yesterday_active_users,
            'member_ratio': round(member_users / total_users * 100, 2) if total_users else 0
        })
        
        # 调用父类方法
        return super().changelist_view(request, extra_context=extra_context)


# 封禁Token管理
class BannedTokenResource(resources.ModelResource):
    class Meta:
        model = BannedToken
        fields = ('id', 'token', 'user__nickname', 'user__wx_phone_new', 'reason', 'detail', 
                 'expire_at', 'is_permanent', 'related_phone', 'related_openid', 
                 'ip_address', 'admin_operator', 'created_at')
        export_order = fields


@admin.register(BannedToken)
class BannedTokenAdmin(ImportExportModelAdmin):
    resource_class = BannedTokenResource
    list_display = ('token_preview', 'user_display', 'reason_display', 'expire_status', 
                   'ip_address', 'admin_operator', 'created_at')
    list_filter = ('reason', 'is_permanent', 'created_at')
    search_fields = ('token', 'detail', 'related_phone', 'related_openid', 'ip_address', 
                    'user__nickname', 'user__wx_phone_new')
    readonly_fields = ('created_at',)
    autocomplete_fields = ['user']
    actions = ['remove_ban', 'make_permanent', 'extend_ban_30_days']
    
    # 添加调试语句，记录请求过程
    def changelist_view(self, request, extra_context=None):
        try:
            return super().changelist_view(request, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin错误: {error_msg}")
            # 记录到日志
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin错误: {error_msg}")
            # 重新抛出异常，让Django处理
            raise
    
    def add_view(self, request, form_url='', extra_context=None):
        try:
            return super().add_view(request, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin添加视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin添加视图错误: {error_msg}")
            raise
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        try:
            return super().change_view(request, object_id, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin修改视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin修改视图错误: {error_msg}")
            raise
    
    fieldsets = (
        ('封禁信息', {
            'fields': ('token', 'user', 'reason', 'detail', 'admin_operator')
        }),
        ('时间设置', {
            'fields': ('expire_at', 'is_permanent', 'created_at')
        }),
        ('关联信息', {
            'fields': ('related_phone', 'related_openid', 'ip_address')
        }),
    )
    
    def token_preview(self, obj):
        """显示Token的前10个字符"""
        if obj.token:
            return f"{obj.token[:10]}..."
        return "无Token"
    token_preview.short_description = 'Token'
    
    def user_display(self, obj):
        """显示用户信息"""
        if obj.user:
            return f"{obj.user.nickname or '未知'} (ID: {obj.user.id})"
        elif obj.related_phone:
            return f"手机: {obj.related_phone}"
        elif obj.related_openid:
            return f"OpenID: {obj.related_openid[:8]}..."
        return "未关联用户"
    user_display.short_description = '用户'
    
    def reason_display(self, obj):
        """显示封禁原因"""
        return obj.get_reason_display()
    reason_display.short_description = '原因'
    
    def expire_status(self, obj):
        """显示封禁状态"""
        if obj.is_permanent:
            return "永久封禁"
        elif obj.expire_at:
            if obj.expire_at > timezone.now():
                days_left = (obj.expire_at - timezone.now()).days
                return f"还有{days_left}天到期"
            else:
                return "已过期"
        return "未设置"
    expire_status.short_description = '状态'
    
    def remove_ban(self, request, queryset):
        """解除封禁 - 彻底删除记录"""
        count = queryset.count()
        # 获取相关用户，用于记录日志
        user_ids = []
        for token in queryset:
            if token.user:
                user_ids.append(str(token.user.id))
        
        # 删除记录
        deleted = queryset.delete()[0]
        
        user_info = f"(用户ID: {', '.join(user_ids)})" if user_ids else ""
        messages.success(request, f"成功删除 {deleted} 个Token的封禁记录 {user_info}")
    remove_ban.short_description = "解除并删除选中Token的封禁"
    
    def make_permanent(self, request, queryset):
        """设为永久封禁"""
        updated = queryset.update(is_permanent=True)
        messages.success(request, f"成功将{updated}个Token设为永久封禁")
    make_permanent.short_description = "将选中Token设为永久封禁"
    
    def extend_ban_30_days(self, request, queryset):
        """延长封禁30天"""
        for token in queryset:
            if token.expire_at and token.expire_at > timezone.now():
                token.expire_at = token.expire_at + timedelta(days=30)
            else:
                token.expire_at = timezone.now() + timedelta(days=30)
            token.save()
        messages.success(request, f"成功延长{queryset.count()}个Token的封禁时间30天")
    extend_ban_30_days.short_description = "延长选中Token的封禁时间30天"


# 封禁账号管理
class BannedAccountResource(resources.ModelResource):
    class Meta:
        model = BannedAccount
        fields = ('id', 'phone_number', 'wx_openid', 'wx_unionid', 'user_id', 
                 'reason', 'detail', 'banned_at', 'expire_at', 'is_permanent', 'ban_count', 
                 'ip_address', 'admin_operator')
        export_order = fields


@admin.register(BannedAccount)
class BannedAccountAdmin(ImportExportModelAdmin):
    resource_class = BannedAccountResource
    list_display = ('account_info', 'reason_display', 'expire_status', 
                   'ban_count', 'admin_operator', 'banned_at')
    list_filter = ('reason', 'is_permanent', 'banned_at')
    search_fields = ('phone_number', 'wx_openid', 'wx_unionid', 'detail', 
                    'ip_address', 'user_id')
    readonly_fields = ('ban_count', 'banned_at')
    actions = ['remove_ban', 'make_permanent', 'extend_ban_30_days']
    
    # 添加调试语句，记录请求过程
    def changelist_view(self, request, extra_context=None):
        try:
            return super().changelist_view(request, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin错误: {error_msg}")
            # 记录到日志
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin错误: {error_msg}")
            # 重新抛出异常，让Django处理
            raise
    
    def add_view(self, request, form_url='', extra_context=None):
        try:
            return super().add_view(request, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin添加视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin添加视图错误: {error_msg}")
            raise
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        try:
            return super().change_view(request, object_id, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin修改视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin修改视图错误: {error_msg}")
            raise
    
    fieldsets = (
        ('账号信息', {
            'fields': ('phone_number', 'wx_openid', 'wx_unionid', 'user_id')
        }),
        ('封禁信息', {
            'fields': ('reason', 'detail', 'admin_operator', 'ban_count')
        }),
        ('时间设置', {
            'fields': ('banned_at', 'expire_at', 'is_permanent')
        }),
        ('其他信息', {
            'fields': ('ip_address', 'device_info')
        }),
    )
    
    def account_info(self, obj):
        """显示账号信息"""
        info = []
        if obj.user_id:
            info.append(f"用户ID: {obj.user_id}")
        if obj.phone_number:
            info.append(f"手机: {obj.phone_number}")
        if obj.wx_openid:
            info.append(f"OpenID: {obj.wx_openid[:8]}...")
        return " | ".join(info) if info else "未知账号"
    account_info.short_description = '账号信息'
    
    def reason_display(self, obj):
        """显示封禁原因"""
        return obj.get_reason_display()
    reason_display.short_description = '原因'
    
    def expire_status(self, obj):
        """显示封禁状态"""
        if obj.is_permanent:
            return "永久封禁"
        elif obj.expire_at:
            if obj.expire_at > timezone.now():
                days_left = (obj.expire_at - timezone.now()).days
                return f"还有{days_left}天到期"
            else:
                return "已过期"
        return "未设置"
    expire_status.short_description = '状态'
    
    def remove_ban(self, request, queryset):
        """解除封禁 - 彻底删除记录"""
        # 获取相关用户ID，用于记录日志
        user_ids = []
        for account in queryset:
            if account.user_id:
                user_ids.append(str(account.user_id))
            
            # 如果有关联的用户ID，同时更新用户的is_banned状态
            if account.user_id:
                try:
                    from ..models import UserInfo
                    user = UserInfo.objects.filter(id=account.user_id).first()
                    if user and user.is_banned:
                        user.is_banned = False
                        user.ban_reason = None
                        user.ban_until = None
                        user.save()
                except Exception as e:
                    print(f"更新用户 {account.user_id} 的封禁状态失败: {e}")
        
        # 删除记录
        deleted = queryset.delete()[0]
        
        user_info = f"(用户ID: {', '.join(user_ids)})" if user_ids else ""
        messages.success(request, f"成功删除 {deleted} 个账号的封禁记录 {user_info}")
    remove_ban.short_description = "解除并删除选中账号的封禁"
    
    def make_permanent(self, request, queryset):
        """设为永久封禁"""
        updated = queryset.update(is_permanent=True)
        messages.success(request, f"成功将{updated}个账号设为永久封禁")
    make_permanent.short_description = "将选中账号设为永久封禁"
    
    def extend_ban_30_days(self, request, queryset):
        """延长封禁30天"""
        for account in queryset:
            if account.expire_at and account.expire_at > timezone.now():
                account.expire_at = account.expire_at + timedelta(days=30)
            else:
                account.expire_at = timezone.now() + timedelta(days=30)
            account.save()
        messages.success(request, f"成功延长{queryset.count()}个账号的封禁时间30天")
    extend_ban_30_days.short_description = "延长选中账号的封禁时间30天" 