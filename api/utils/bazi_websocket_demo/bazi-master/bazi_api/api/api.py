from ninja import Router
from ninja.schema import Schema
from typing import List, Dict, Optional, Literal
import os
import sys
# 添加路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)
# 绝对导入metaphysic模块
import metaphysic
from datetime import datetime
import json
import traceback
import asyncio
import openai
from django.conf import settings
import pytz
# 导入提示词配置
from api.utils.constants import get_api_system_message, get_api_user_prompt

# 导入算卦服务
from api.services.divination_core_service import divination_core_service
from api.utils.constants import get_divination_system_prompt, get_divination_prompt

# 导入完整的五运六气系统
try:
    from bazi_api.bazi_api.complete_wuyunliuqi import great_contribution as wuyunliuqi_core
except ImportError as e:
    print(f"导入complete_wuyunliuqi模块失败: {e}")
    print(f"当前路径: {sys.path}")
    # 如果导入失败，定义一个空函数作为替代
    class EmptyModule:
        def getWuYunLiuQi(self, date_str):
            return json.dumps([{"error": "无法导入五运六气模块"}], ensure_ascii=False)
    wuyunliuqi_core = EmptyModule()

# 从config模块获取配置（适配主项目的openai使用方式）
from api.utils.config import MODEL_ID

router = Router()

# 算卦相关的输入模型
class DivinationSchema(Schema):
    user_number: int  # 用户输入的数字 (0-999)
    question: Optional[str] = None  # 用户的问题（可选）

class DivinationInterpretationSchema(Schema):
    user_number: int  # 用户输入的数字 (0-999)
    question: str  # 用户的问题（必填）
    gua_number: Optional[int] = None  # 指定卦数（可选，如果不指定则根据user_number生成）

# 算卦相关的输出模型
class DivinationResultSchema(Schema):
    success: bool
    user_input: int
    gua_number: int
    gua_info: Dict
    divination_info: Dict
    summary: Dict
    timestamp: str

# 输入模型
class BirthInfoSchema(Schema):
    birth_year: int
    birth_month: int
    birth_day: int
    birth_time: int
    need_advice: bool = False  # 是否需要中医养生建议
    include_wuyun_liuqi: bool = False  # 是否包含五运六气信息
    consult_type: Optional[str] = "health"  # 咨询类型：健康咨询(默认)、事业咨询、学业咨询、风水咨询、运势咨询

# 五运六气模型
class WuYunLiuQiSchema(Schema):
    wuyun: str  # 五运
    liuqi: str  # 六气

# 输出模型
class BaziSchema(Schema):
    shenchenbazi: str
    wuxing: Dict[str, float]
    shengxiao: str
    health_advice: Optional[str] = None  # 可选的中医养生建议
    career_advice: Optional[str] = None  # 可选的事业咨询建议
    education_advice: Optional[str] = None  # 可选的学业咨询建议
    fengshui_advice: Optional[str] = None  # 可选的风水咨询建议
    fortune_advice: Optional[str] = None  # 可选的运势咨询建议
    current_wuyun_liuqi: Optional[Dict[str, str]] = None  # 当前五运六气

# 获取卦象解读建议
def get_divination_interpretation(gua_info, question, interpretation_type="general_interpretation"):
    """
    根据卦象信息和用户问题，调用华为方舟AI获取卦象解读
    """
    try:
        # 根据解读类型构建不同的系统提示和用户提示
        system_message = get_divination_system_prompt("general")
        user_prompt = ""
        
        if interpretation_type == "general_interpretation":
            user_prompt = get_divination_prompt("general_interpretation").format(
                gua_name=gua_info['name'],
                gua_code=gua_info['code'],
                question=question if question else "请为我解读这一卦的含义",
                gua_ci=gua_info['gua_ci']
            )
        elif interpretation_type == "specific_question":
            user_prompt = get_divination_prompt("specific_question").format(
                gua_name=gua_info['name'],
                gua_number=gua_info['id'],
                gua_code=gua_info['code'],
                question=question,
                gua_ci=gua_info['gua_ci']
            )
        elif interpretation_type == "life_guidance":
            user_prompt = get_divination_prompt("life_guidance").format(
                gua_name=gua_info['name'],
                gua_number=gua_info['id'],
                gua_code=gua_info['code'],
                question=question if question else "请为我的人生提供指导",
                gua_ci=gua_info['gua_ci']
            )
        else:
            # 默认使用通用解读
            user_prompt = get_divination_prompt("general_interpretation").format(
                gua_name=gua_info['name'],
                gua_code=gua_info['code'],
                question=question if question else "请为我解读这一卦的含义",
                gua_ci=gua_info['gua_ci']
            )
        
        # 构建对话历史
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]
        
        # 调用华为方舟API获取回复 (非流式)
        completion = openai.ChatCompletion.create(
            model=MODEL_ID,
            messages=messages,
            max_tokens=1500,
            temperature=0.7
        )
        
        # 提取回复内容
        interpretation = completion.choices[0].message.content
        return interpretation
    
    except Exception as e:
        error_message = f"获取卦象解读时出错: {str(e)}\n{traceback.format_exc()}"
        print(error_message)
        return f"无法获取卦象解读，发生错误: {str(e)}"

@router.post("/divination/interpret", summary="卦象解读", description="根据卦象信息和用户问题生成详细解读")
def interpret_divination(request, interpretation_input: DivinationInterpretationSchema):
    """
    根据卦象信息和用户问题生成详细解读
    
    Args:
        interpretation_input: 包含用户输入数字、问题和可选卦数的数据
        
    Returns:
        卦象解读结果
    """
    try:
        # 验证用户输入
        is_valid, user_input, error_msg = divination_core_service.validate_user_input(interpretation_input.user_number)
        
        if not is_valid:
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
        
        # 获取卦象信息
        if interpretation_input.gua_number:
            # 使用指定的卦数
            gua_result = divination_core_service.get_gua_by_number(interpretation_input.gua_number)
            if not gua_result['success']:
                return gua_result
            gua_info = gua_result['gua_info']
            gua_number = interpretation_input.gua_number
        else:
            # 根据用户输入生成卦数
            divination_result = divination_core_service.perform_divination(user_input, interpretation_input.question)
            if not divination_result['success']:
                return divination_result
            gua_info = divination_result['gua_info']
            gua_number = divination_result['divination_info']['gua_number']
        
        # 根据问题类型选择解读方式
        interpretation_type = "specific_question" if interpretation_input.question else "general_interpretation"
        
        # 获取卦象解读
        interpretation = get_divination_interpretation(gua_info, interpretation_input.question, interpretation_type)
        
        # 构建响应
        result = {
            'success': True,
            'divination_info': {
                'user_input': user_input,
                'question': interpretation_input.question,
                'gua_number': gua_number,
                'interpretation_type': interpretation_type,
                'timestamp': datetime.now().isoformat()
            },
            'gua_info': gua_info,
            'interpretation': interpretation,
            'summary': {
                'gua_name': gua_info['name'],
                'gua_code': gua_info['code'],
                'brief_interpretation': interpretation[:200] + '...' if len(interpretation) > 200 else interpretation
            }
        }
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"卦象解读API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

@router.get("/divination/interpret/{gua_number}", summary="指定卦象解读", description="根据指定的卦数获取卦象解读")
def interpret_gua_by_number(request, gua_number: int, question: str = "请为我解读这一卦的含义"):
    """
    根据指定的卦数获取卦象解读
    
    Args:
        gua_number: 卦数 (1-64)
        question: 用户问题（可选）
        
    Returns:
        卦象解读结果
    """
    try:
        # 获取卦象信息
        gua_result = divination_core_service.get_gua_by_number(gua_number)
        if not gua_result['success']:
            return gua_result
        
        gua_info = gua_result['gua_info']
        
        # 根据问题选择解读类型
        interpretation_type = "specific_question" if question != "请为我解读这一卦的含义" else "general_interpretation"
        
        # 获取卦象解读
        interpretation = get_divination_interpretation(gua_info, question, interpretation_type)
        
        # 构建响应
        result = {
            'success': True,
            'divination_info': {
                'gua_number': gua_number,
                'question': question,
                'interpretation_type': interpretation_type,
                'timestamp': datetime.now().isoformat()
            },
            'gua_info': gua_info,
            'interpretation': interpretation,
            'summary': {
                'gua_name': gua_info['name'],
                'gua_code': gua_info['code'],
                'brief_interpretation': interpretation[:200] + '...' if len(interpretation) > 200 else interpretation
            }
        }
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"指定卦象解读API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

# 算卦API接口
@router.post("/divination", summary="算卦", description="根据用户输入的数字进行算卦")
def perform_divination(request, divination_input: DivinationSchema):
    """
    根据用户输入的数字进行算卦
    
    Args:
        divination_input: 包含用户输入数字和问题的数据
        
    Returns:
        算卦结果
    """
    try:
        # 验证用户输入
        is_valid, user_input, error_msg = divination_core_service.validate_user_input(divination_input.user_number)
        
        if not is_valid:
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
        
        # 执行算卦
        result = divination_core_service.perform_divination(user_input, divination_input.question)
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"算卦API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

@router.get("/divination/simple/{user_number}", summary="简单算卦", description="根据用户输入的数字进行简单算卦")
def get_simple_divination(request, user_number: int):
    """
    简单算卦接口，只返回基本卦象信息
    
    Args:
        user_number: 用户输入的数字 (0-999)
        
    Returns:
        简化的算卦结果
    """
    try:
        # 验证用户输入
        is_valid, user_input, error_msg = divination_core_service.validate_user_input(user_number)
        
        if not is_valid:
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
        
        # 执行简单算卦
        result = divination_core_service.get_simple_divination(user_input)
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"简单算卦API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

@router.get("/divination/gua/{gua_number}", summary="获取卦象信息", description="根据卦数获取详细卦象信息")
def get_gua_info(request, gua_number: int):
    """
    根据卦数获取详细卦象信息
    
    Args:
        gua_number: 卦数 (1-64)
        
    Returns:
        卦象详细信息
    """
    try:
        result = divination_core_service.get_gua_by_number(gua_number)
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': f"获取卦象信息API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

@router.get("/divination/stats", summary="算卦系统统计", description="获取算卦系统的统计信息")
def get_divination_stats(request):
    """
    获取算卦系统统计信息
    
    Returns:
        系统统计信息
    """
    try:
        stats = divination_core_service.get_divination_statistics()
        return stats
        
    except Exception as e:
        return {
            'error': f"获取统计信息API出错: {str(e)}",
            'timestamp': datetime.now().isoformat()
        }

# WebSocket支持API
@router.get("/divination/stream", summary="流式算卦解读(GET)", description="通过WebSocket提供流式算卦解读")
@router.post("/divination/stream", summary="流式算卦解读(POST)", description="通过WebSocket提供流式算卦解读")
def stream_divination(request, divination_input: DivinationSchema):
    """
    提供WebSocket端点的信息，用于前端连接获取流式算卦解读
    
    可通过 GET 或 POST 请求访问，需按如下格式连接WebSocket:
    ws://服务器地址/ws/divination/
    
    同时提供了完整的前端界面，可通过访问 /divination/stream/ 使用网页版流式算卦解读
    """
    return {
        "message": "请通过WebSocket连接到 ws://服务器地址/ws/divination/ 获取流式响应，或访问 /divination/stream/ 使用网页版",
        "connection_info": {
            "ws_url": "/ws/divination/",
            "frontend_url": "/divination/stream/",
            "divination_info": {
                "user_number": divination_input.user_number,
                "question": divination_input.question
            }
        },
        "usage_examples": {
            "divination_request": {
                "divination_request": {
                    "user_number": divination_input.user_number,
                    "question": divination_input.question
                }
            },
            "interpretation_request": {
                "interpretation_request": {
                    "gua_number": 1,
                    "question": "请为我解读这一卦",
                    "interpretation_type": "general_interpretation"
                }
            }
        }
    }

# 获取各类型咨询建议
def get_consult_advice(bazi_info, wuxing_info, consult_type="health", wuyun_liuqi=None):
    """
    根据八字、五行信息和咨询类型，调用华为方舟AI获取相应的咨询建议
    """
    try:
        # 根据咨询类型构建不同的系统提示和用户提示
        system_message = ""
        user_prompt = ""
        
        if consult_type == "health":
            system_message = get_api_system_message("health")

            if wuyun_liuqi:
                user_prompt = get_api_user_prompt("health", "with_wuyunliuqi_simple").format(
                    shenchenbazi=bazi_info,
                    wuyun=wuyun_liuqi['五运'],
                    liuqi=wuyun_liuqi['六气']
                )
            else:
                user_prompt = get_api_user_prompt("health", "without_wuyunliuqi").format(
                    shenchenbazi=bazi_info
                )
        
        elif consult_type == "career":
            system_message = get_api_system_message("career")
            
            if wuyun_liuqi:
                user_prompt = get_api_user_prompt("career", "with_wuyunliuqi_simple").format(
                    shenchenbazi=bazi_info,
                    wuyun=wuyun_liuqi['五运'],
                    liuqi=wuyun_liuqi['六气']
                )
            else:
                user_prompt = get_api_user_prompt("career", "without_wuyunliuqi").format(
                    shenchenbazi=bazi_info
                )
        
        elif consult_type == "education":
            system_message = get_api_system_message("education")
            
            if wuyun_liuqi:
                user_prompt = get_api_user_prompt("education", "with_wuyunliuqi_simple").format(
                    shenchenbazi=bazi_info,
                    wuyun=wuyun_liuqi['五运'],
                    liuqi=wuyun_liuqi['六气']
                )
            else:
                user_prompt = get_api_user_prompt("education", "without_wuyunliuqi").format(
                    shenchenbazi=bazi_info
                )
        
        elif consult_type == "fengshui":
            system_message = get_api_system_message("fengshui")
            
            if wuyun_liuqi:
                user_prompt = get_api_user_prompt("fengshui", "with_wuyunliuqi_simple").format(
                    shenchenbazi=bazi_info,
                    wuyun=wuyun_liuqi['五运'],
                    liuqi=wuyun_liuqi['六气']
                )
            else:
                user_prompt = get_api_user_prompt("fengshui", "without_wuyunliuqi").format(
                    shenchenbazi=bazi_info
                )
        
        elif consult_type == "fortune":
            system_message = get_api_system_message("fortune")
            
            if wuyun_liuqi:
                user_prompt = get_api_user_prompt("fortune", "with_wuyunliuqi_simple").format(
                    shenchenbazi=bazi_info,
                    wuyun=wuyun_liuqi['五运'],
                    liuqi=wuyun_liuqi['六气']
                )
            else:
                user_prompt = get_api_user_prompt("fortune", "without_wuyunliuqi").format(
                    shenchenbazi=bazi_info
                )
        
        else:
            # 默认使用健康咨询
            system_message = get_api_system_message("health")
            user_prompt = get_api_user_prompt("health", "without_wuyunliuqi").format(
                shenchenbazi=bazi_info
            )
        
        # 构建对话历史
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]
        
        # 调用华为方舟API获取回复 (非流式)
        completion = openai.ChatCompletion.create(
            model=MODEL_ID,
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )
        
        # 提取回复内容
        advice = completion.choices[0].message.content
        return advice
    
    except Exception as e:
        error_message = f"获取{consult_type}咨询建议时出错: {str(e)}\n{traceback.format_exc()}"
        print(error_message)
        return f"无法获取{consult_type}咨询建议，发生错误: {str(e)}"

# 保留兼容性的旧函数
def get_health_advice(bazi_info, wuxing_info, wuyun_liuqi=None):
    """
    根据八字、五行信息和五运六气（如果有），调用华为方舟AI获取中医养生建议
    """
    return get_consult_advice(bazi_info, wuxing_info, "health", wuyun_liuqi)

@router.post("/bazi", response=BaziSchema, summary="八字分析", description="根据年月日时分析生辰八字和五行，可选择获取中医养生建议和五运六气")
def analyze_bazi(request, birth_info: BirthInfoSchema):
    """
    根据用户输入的出生年月日时，计算生辰八字和五行，并可选择获取各种咨询建议和五运六气
    """
    try:
        # 获取八字
        shenchenbazi = metaphysic.getShenChenBaZi(
            birth_info.birth_year, 
            birth_info.birth_month, 
            birth_info.birth_day, 
            birth_info.birth_time
        )
        
        # 获取生肖
        # 这里简化处理，通过计算年份获取生肖
        shengxiao_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        shengxiao = shengxiao_list[(birth_info.birth_year - 4) % 12]
        
        # 计算五行
        wuxing_tuple = metaphysic.getWuXing(shenchenbazi)
        wuxing_dict = {
            "金": wuxing_tuple[0],
            "木": wuxing_tuple[1],
            "水": wuxing_tuple[2],
            "火": wuxing_tuple[3],
            "土": wuxing_tuple[4]
        }
        
        # 准备响应数据
        response_data = {
            "shenchenbazi": shenchenbazi,
            "wuxing": wuxing_dict,
            "shengxiao": shengxiao
        }
        
        # 如果需要五运六气信息
        wuyun_liuqi = None
        if birth_info.include_wuyun_liuqi:
            # 使用新的完整五运六气系统
            try:
                # 构建日期字符串
                date_str = f"{birth_info.birth_year}-{birth_info.birth_month}-{birth_info.birth_day}"
                
                # 调用完整五运六气系统
                result_json = wuyunliuqi_core.getWuYunLiuQi(date_str)
                result_data = json.loads(result_json)[0]
                
                # 将结果保存到响应数据中
                response_data["current_wuyun_liuqi"] = result_data
                
            except Exception as e:
                # 如果新系统出错，回退到旧系统
                print(f"使用完整五运六气系统出错：{str(e)}，回退到原系统")
                wuyun_liuqi = metaphysic.getCurrentWuYunLiuQi()
                response_data["current_wuyun_liuqi"] = wuyun_liuqi
                response_data["wuyun_liuqi_note"] = "注意：使用备用计算方法，完整系统出错"
        
        # 如果需要咨询建议，根据咨询类型调用不同的API
        if birth_info.need_advice:
            consult_type = birth_info.consult_type if birth_info.consult_type else "health"
            
            if consult_type == "health" or consult_type == "all":
                health_advice = get_consult_advice(shenchenbazi, wuxing_dict, "health", wuyun_liuqi)
                response_data["health_advice"] = health_advice
                
            if consult_type == "career" or consult_type == "all":
                career_advice = get_consult_advice(shenchenbazi, wuxing_dict, "career", wuyun_liuqi)
                response_data["career_advice"] = career_advice
                
            if consult_type == "education" or consult_type == "all":
                education_advice = get_consult_advice(shenchenbazi, wuxing_dict, "education", wuyun_liuqi)
                response_data["education_advice"] = education_advice
                
            if consult_type == "fengshui" or consult_type == "all":
                fengshui_advice = get_consult_advice(shenchenbazi, wuxing_dict, "fengshui", wuyun_liuqi)
                response_data["fengshui_advice"] = fengshui_advice
                
            if consult_type == "fortune" or consult_type == "all":
                fortune_advice = get_consult_advice(shenchenbazi, wuxing_dict, "fortune", wuyun_liuqi)
                response_data["fortune_advice"] = fortune_advice
        
        return response_data
        
    except Exception as e:
        # Django Ninja会自动处理异常并返回适当的HTTP状态码
        raise ValueError(f"八字分析出错: {str(e)}")

# WebSocket支持API（修改为支持GET请求）
@router.get("/bazi/stream", summary="流式八字分析(GET)", description="通过WebSocket提供流式八字分析和中医养生建议")
@router.post("/bazi/stream", summary="流式八字分析(POST)", description="通过WebSocket提供流式八字分析和中医养生建议")
def stream_bazi_advice(request, birth_info: BirthInfoSchema):
    """
    提供WebSocket端点的信息，用于前端连接获取流式响应
    
    可通过 GET 或 POST 请求访问，需按如下格式连接WebSocket:
    ws://服务器地址/ws/bazi_advice/
    
    同时提供了完整的前端界面，可通过访问 /bazi/stream/ 使用网页版流式八字分析
    """
    return {
        "message": "请通过WebSocket连接到 ws://服务器地址/ws/bazi_advice/ 获取流式响应，或访问 /bazi/stream/ 使用网页版",
        "connection_info": {
            "ws_url": "/ws/bazi_advice/",
            "frontend_url": "/bazi/stream/",
            "birth_info": {
                "birth_year": birth_info.birth_year,
                "birth_month": birth_info.birth_month,
                "birth_day": birth_info.birth_day,
                "birth_time": birth_info.birth_time,
                "include_wuyun_liuqi": birth_info.include_wuyun_liuqi
            }
        }
    }

# 简单的健康检查API
@router.get("/health", summary="健康检查")
def health_check(request):
    """健康检查API"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()} 

# 获取当前五运六气API
@router.get("/wuyun-liuqi", summary="获取当前五运六气", description="获取当前时间（东八区）的五运六气信息")
def get_current_wuyun_liuqi(request):
    """
    获取当前时间（东八区）的五运六气信息
    """
    try:
        # 获取当前时间
        current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
        date_str = current_time.strftime("%Y-%m-%d")
        
        # 使用完整的五运六气系统获取信息
        result_json = wuyunliuqi_core.getWuYunLiuQi(date_str)
        result_data = json.loads(result_json)
        
        # 准备响应数据
        response_data = {
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "wuyun_liuqi": result_data[0],
            "timestamp": datetime.now().isoformat()
        }
        
        return response_data
    
    except Exception as e:
        # Django Ninja会自动处理异常并返回适当的HTTP状态码
        raise ValueError(f"获取五运六气信息出错: {str(e)}") 

@router.get("/wuxing/current", summary="获取当前五运六气")
def get_current_wuxing():
    """
    获取当前时间的五运六气信息
    返回格式：
    {
        "time": "2024-03-21 14:30:00",
        "wuxing": "木运不及",
        "liuqi": "少阳相火司天，厥阴风木在泉"
    }
    """
    # 获取当前时间（东八区）
    current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
    date_str = current_time.strftime("%Y-%m-%d")
    
    # 使用完整的五运六气系统获取信息
    try:
        result_json = wuyunliuqi_core.getWuYunLiuQi(date_str)
        result_data = json.loads(result_json)[0]
        
        return {
            "time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "wuxing": result_data.get('dayun', '未知'),
            "liuqi": f"{result_data.get('sitian', '未知')}司天，{result_data.get('zaiquan', '未知')}在泉"
        }
    except Exception as e:
        # 备用方案：如果新系统出错，使用旧的计算方法
        wuxing_result = getCurrentWuXing(current_time.year)
        
        return {
            "time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "wuxing": wuxing_result["wuxing"],
            "liuqi": wuxing_result["liuqi"],
            "error": f"注意：使用备用计算方法，完整系统出错: {str(e)}"
        }

# 定义getCurrentWuXing函数，用于获取特定年份的五运六气 (保留作为备用方法)
def getCurrentWuXing(year):
    """
    根据年份计算五运六气
    
    Args:
        year (int): 年份
        
    Returns:
        dict: 包含wuxing和liuqi的字典
    """
    # 五运六气的计算逻辑
    # 这里是简化实现，实际应包含完整的五运六气计算
    year_mod_10 = year % 10  # 天干
    year_mod_12 = year % 12  # 地支
    
    # 五运对应表（根据干支纪年推算）
    wuxing_map = {
        0: "土运太过",
        1: "金运不及",
        2: "金运太过",
        3: "水运不及",
        4: "水运太过",
        5: "木运不及",
        6: "木运太过",
        7: "火运不及",
        8: "火运太过",
        9: "土运不及"
    }
    
    # 六气对应表（简化版）
    liuqi_map = {
        0: "太阳寒水司天，阳明燥金在泉",
        1: "厥阴风木司天，太阴湿土在泉",
        2: "少阴君火司天，少阳相火在泉",
        3: "太阴湿土司天，厥阴风木在泉",
        4: "少阳相火司天，少阴君火在泉",
        5: "阳明燥金司天，太阳寒水在泉"
    }
    
    wuxing = wuxing_map[year_mod_10]
    liuqi = liuqi_map[year_mod_12 % 6]
    
    return {
        "wuxing": wuxing,
        "liuqi": liuqi
    }

@router.get("/test_wuxing")
def test_wuxing():
    """
    测试五运六气计算功能
    
    Returns:
        dict: 包含测试结果的字典
    """
    try:
        # 测试当前年份
        current_result = get_current_wuxing()
        
        # 测试特定年份
        test_years = [2024, 2025, 2026]
        test_results = {}
        for year in test_years:
            date_str = f"{year}-6-6"  # 选择年中的日期
            try:
                result_json = wuyunliuqi_core.getWuYunLiuQi(date_str)
                result_data = json.loads(result_json)[0]
                test_results[str(year)] = {
                    "dayun": result_data.get('dayun', '未知'),
                    "sitian": result_data.get('sitian', '未知'),
                    "zaiquan": result_data.get('zaiquan', '未知'),
                    "qi_shunxu": result_data.get('qi_shunxu', '未知')
                }
            except Exception as e:
                # 备用方法
                result = getCurrentWuXing(year)
                test_results[str(year)] = {
                    "wuxing": result["wuxing"],
                    "liuqi": result["liuqi"],
                    "error": f"使用备用计算方法: {str(e)}"
                }
            
        return {
            "current": current_result,
            "test_cases": test_results,
            "status": "success"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

# 新增五运六气API，允许用户传入指定日期
@router.get("/wuyun-liuqi/{date}", summary="获取指定日期的五运六气信息", description="根据指定日期获取五运六气详细信息")
def get_date_wuyun_liuqi(request, date: str):
    """
    根据指定日期获取五运六气详细信息
    
    参数:
        date: 格式为'YYYY-MM-DD'的日期字符串，如'2025-04-24'
    """
    try:
        # 验证日期格式
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            return {"error": "日期格式不正确，请使用'YYYY-MM-DD'格式"}
        
        # 使用完整的五运六气系统获取信息
        result_json = wuyunliuqi_core.getWuYunLiuQi(date)
        result_data = json.loads(result_json)
        
        return {
            "date": date,
            "wuyun_liuqi": result_data[0],
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        # Django Ninja会自动处理异常并返回适当的HTTP状态码
        raise ValueError(f"获取五运六气信息出错: {str(e)}") 