"""
LLM服务模块
处理大语言模型API调用
"""
import json
import traceback
import asyncio
from api.utils.logging import safe_print
from api.utils.config import client, MODEL_ID

class LLMService:
    """华为方舟大语言模型服务类"""
    
    @staticmethod
    async def process_streaming_response(stream_response, send_callback, message_type):
        """
        处理流式响应
        
        Args:
            stream_response: 流式API响应
            send_callback: 发送数据的回调函数
            message_type: 消息类型
            
        Returns:
            str: 完整的回复内容
        """
        assistant_message = ""
        
        # 处理流式返回的内容
        safe_print(f"开始处理{message_type}流式响应")
        chunk_count = 0
        buffer = ""  # 用于积累字符
        buffer_size = 0  # 当前缓冲区大小
        max_buffer_size = 30  # 减小最大缓冲区大小，使流式效果更明显
        last_sent_time = asyncio.get_event_loop().time()  # 记录最后一次发送时间
        
        try:
            for chunk in stream_response:
                chunk_count += 1
                if not chunk.choices:
                    continue
                
                # 获取内容
                content = chunk.choices[0].delta.content
                if content:
                    # 确保内容是UTF-8编码的字符串
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    # 记录收到的内容（仅限调试）
                    if chunk_count % 10 == 0:  # 每10个块记录一次，避免日志过多
                        safe_print(f"收到第{chunk_count}块内容: {content[:20]}...")
                    
                    assistant_message += content
                    
                    # 将内容添加到缓冲区
                    buffer += content
                    buffer_size += len(content)
                    
                    current_time = asyncio.get_event_loop().time()
                    time_since_last_send = current_time - last_sent_time
                    
                    # 当缓冲区达到一定大小或收到特定字符或经过了足够的时间时发送
                    if (buffer_size >= max_buffer_size or 
                        any(char in buffer for char in ['\n', '。', '，', '；', '：']) or
                        time_since_last_send > 0.5):  # 确保至少每0.5秒发送一次
                        
                        # 立即发送数据块到客户端
                        try:
                            await send_callback(json.dumps({
                                'type': message_type,
                                'content': buffer,
                                'done': False
                            }))
                            safe_print(f"发送{len(buffer)}字符给客户端，类型: {message_type}")
                            last_sent_time = current_time
                        except Exception as e:
                            safe_print(f"发送数据块时出错: {str(e)}")
                        
                        # 清空缓冲区
                        buffer = ""
                        buffer_size = 0
                        
                        # 重要：添加短暂延迟，模拟真实打字速度，让前端有时间处理
                        await asyncio.sleep(0.05)  # 减少到50毫秒延迟，保持流畅但不过慢
                
                # 每50个数据块记录一次进度
                if chunk_count % 50 == 0:
                    safe_print(f"已处理{chunk_count}个数据块")
            
            # 发送剩余缓冲区中的内容（如果有）
            if buffer:
                try:
                    await send_callback(json.dumps({
                        'type': message_type,
                        'content': buffer,
                        'done': False
                    }))
                    safe_print(f"发送最后{len(buffer)}字符给客户端")
                except Exception as e:
                    safe_print(f"发送最后数据块时出错: {str(e)}")
                
                await asyncio.sleep(0.05)  # 最后一次也添加延迟
        
            safe_print(f"流式响应处理完成，共处理{chunk_count}个数据块")
            safe_print(f"完整回复长度: {len(assistant_message)}")
        except Exception as e:
            safe_print(f"处理流式响应时出错: {str(e)}")
            safe_print(traceback.format_exc())
            raise
            
        # 发送完成标记前添加额外延迟，确保前端处理完所有内容
        await asyncio.sleep(0.2)
            
        # 发送完成标记
        try:
            await send_callback(json.dumps({
                'type': message_type,
                'content': '',
                'done': True
            }))
            safe_print(f"发送完成标记, 类型: {message_type}")
        except Exception as e:
            safe_print(f"发送完成标记时出错: {str(e)}")
        
        return assistant_message
    
    @staticmethod
    async def generate_response(messages, send_callback, message_type, max_tokens=2000, temperature=0.7):
        """
        生成LLM回复
        
        Args:
            messages: 对话历史消息
            send_callback: 发送数据的回调函数
            message_type: 消息类型
            max_tokens: 最大生成长度
            temperature: 温度参数
            
        Returns:
            str: 生成的回复内容，如果出错返回错误消息
        """
        try:
            # 对消息内容进行深度检查，确保所有字段都是UTF-8兼容的
            for idx, message in enumerate(messages):
                safe_print(f"检查消息[{idx}]的编码兼容性:")
                for key, value in message.items():
                    if isinstance(value, str):
                        safe_print(f"  - 字段'{key}'类型: {type(value)}, 长度: {len(value)}")
                        if value:
                            # 尝试不同编码测试
                            try:
                                safe_print(f"  - 尝试UTF-8编码: {value[:10].encode('utf-8')}")
                            except UnicodeEncodeError as e:
                                safe_print(f"  - UTF-8编码失败: {e}")
            
            # 尝试将API参数序列化为JSON以验证兼容性
            test_json = json.dumps({"messages": messages})
            safe_print(f"API请求参数可以正确序列化为JSON，长度: {len(test_json)}")
            
            # 确保所有消息内容都可以正确使用JSON序列化
            # 尝试手动规范化消息内容，确保编码兼容性
            sanitized_messages = []
            for msg in messages:
                # 复制消息以避免修改原始对象
                sanitized_msg = {
                    "role": msg["role"],
                    "content": msg["content"]
                }
                
                # 确保能够编码为JSON
                try:
                    json.dumps(sanitized_msg)
                except UnicodeEncodeError:
                    # 如果有编码问题，尝试将内容编码为UTF-8再解码
                    try:
                        sanitized_msg["content"] = sanitized_msg["content"].encode('utf-8', errors='ignore').decode('utf-8')
                        safe_print(f"消息内容被强制编码为UTF-8: {sanitized_msg['content'][:20]}...")
                    except Exception as enc_error:
                        safe_print(f"消息内容编码修复失败: {enc_error}")
                
                sanitized_messages.append(sanitized_msg)
            
            # 调用华为方舟API流式API
            safe_print("开始调用华为方舟API")
            stream = client.chat.completions.create(
                model=MODEL_ID,
                messages=sanitized_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True
            )
            
            # 处理流式响应
            return await LLMService.process_streaming_response(stream, send_callback, message_type)
            
        except Exception as api_error:
            error_detail = str(api_error)
            safe_print(f"调用华为方舟API出错: {error_detail}")
            safe_print(f"错误类型: {type(api_error)}")
            safe_print(f"详细错误信息: {traceback.format_exc()}")
            
            # 提供更友好的错误消息
            friendly_error = "调用大模型API时出错，请稍后再试"
            if "ordinal not in range" in error_detail:
                friendly_error = "API调用编码错误，请确保API密钥正确且支持中文编码"
            
            # 发送错误消息
            await send_callback(json.dumps({
                'type': message_type,
                'content': friendly_error,
                'done': True
            }))
            
            return friendly_error 