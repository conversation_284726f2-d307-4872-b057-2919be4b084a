"""
算卦WebSocket消费者模块
处理算卦相关WebSocket请求
"""
import json
import traceback
from datetime import datetime
from api.utils.logging import safe_print
from api.utils.constants import get_divination_system_prompt, get_divination_prompt
from api.services.divination_core_service import divination_core_service
from api.services.llm_service import LLMService
from api.websockets.base_consumer import BaseConsumer

class DivinationConsumer(BaseConsumer):
    """算卦WebSocket消费者类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 默认使用通用算卦系统提示
        self.conversation_history = [
            {"role": "system", "content": get_divination_system_prompt("general")}
        ]
        
        # 当前解读类型
        self.current_interpretation_type = "general_interpretation"
    
    async def handle_message(self, data):
        """
        处理收到的消息
        
        Args:
            data (dict): 收到的消息数据
        """
        # 如果是算卦请求
        if 'divination_request' in data:
            safe_print(f"处理算卦请求: {data['divination_request']}")
            await self.handle_divination_request(data['divination_request'])
        # 如果是卦象解读请求
        elif 'interpretation_request' in data:
            safe_print(f"处理卦象解读请求: {data['interpretation_request']}")
            await self.handle_interpretation_request(data['interpretation_request'])
        # 如果是普通聊天请求
        elif 'message' in data:
            message = data['message']
            safe_print(f"处理聊天消息: {message[:50]}...")
            # 确保消息是UTF-8编码的字符串
            if isinstance(message, bytes):
                message = message.decode('utf-8')
            await self.handle_chat_message(message)
        else:
            safe_print(f"无法识别的请求格式: {data}")
            await self.send_error('无法识别的请求格式')
    
    async def handle_divination_request(self, divination_request):
        """
        处理算卦请求
        
        Args:
            divination_request (dict): 算卦请求数据
        """
        try:
            # 提取请求信息
            user_number = divination_request.get('user_number')
            question = divination_request.get('question')
            
            # 验证用户输入
            is_valid, user_input, error_msg = divination_core_service.validate_user_input(user_number)
            
            if not is_valid:
                await self.send_error(error_msg)
                return
            
            # 执行算卦
            result = divination_core_service.perform_divination(user_input, question)
            
            if not result['success']:
                await self.send_error(result['error'])
                return
            
            # 发送算卦结果
            await self.send(text_data=json.dumps({
                'type': 'divination_result',
                'data': result,
                'done': False
            }))
            
            safe_print(f"算卦完成 - 用户输入: {user_input}, 得卦: {result['gua_info']['name']}")
            
            # 如果有问题，自动进行解读
            if question:
                await self.perform_streaming_interpretation(
                    result['gua_info'], 
                    question, 
                    "specific_question"
                )
            else:
                # 发送完成标记
                await self.send(text_data=json.dumps({
                    'type': 'divination_complete',
                    'done': True
                }))
            
        except Exception as e:
            error_message = f"处理算卦请求时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            await self.send_error(error_message)
    
    async def handle_interpretation_request(self, interpretation_request):
        """
        处理卦象解读请求
        
        Args:
            interpretation_request (dict): 解读请求数据
        """
        try:
            # 提取请求信息
            gua_number = interpretation_request.get('gua_number')
            question = interpretation_request.get('question', '请为我解读这一卦的含义')
            interpretation_type = interpretation_request.get('interpretation_type', 'general_interpretation')
            
            # 获取卦象信息
            gua_result = divination_core_service.get_gua_by_number(gua_number)
            if not gua_result['success']:
                await self.send_error(gua_result['error'])
                return
            
            gua_info = gua_result['gua_info']
            
            # 发送卦象基本信息
            await self.send(text_data=json.dumps({
                'type': 'gua_info',
                'data': {
                    'gua_info': gua_info,
                    'question': question,
                    'interpretation_type': interpretation_type
                },
                'done': False
            }))
            
            # 执行流式解读
            await self.perform_streaming_interpretation(gua_info, question, interpretation_type)
            
        except Exception as e:
            error_message = f"处理卦象解读请求时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            await self.send_error(error_message)
    
    async def perform_streaming_interpretation(self, gua_info, question, interpretation_type):
        """
        执行流式卦象解读
        
        Args:
            gua_info (dict): 卦象信息
            question (str): 用户问题
            interpretation_type (str): 解读类型
        """
        try:
            # 构建系统提示和用户提示
            system_message = get_divination_system_prompt("general")
            user_prompt = ""
            
            if interpretation_type == "general_interpretation":
                user_prompt = get_divination_prompt("general_interpretation").format(
                    gua_name=gua_info['name'],
                    gua_code=gua_info['code'],
                    question=question,
                    gua_ci=gua_info['gua_ci']
                )
            elif interpretation_type == "specific_question":
                user_prompt = get_divination_prompt("specific_question").format(
                    gua_name=gua_info['name'],
                    gua_number=gua_info['id'],
                    gua_code=gua_info['code'],
                    question=question,
                    gua_ci=gua_info['gua_ci']
                )
            elif interpretation_type == "life_guidance":
                user_prompt = get_divination_prompt("life_guidance").format(
                    gua_name=gua_info['name'],
                    gua_number=gua_info['id'],
                    gua_code=gua_info['code'],
                    question=question,
                    gua_ci=gua_info['gua_ci']
                )
            else:
                user_prompt = get_divination_prompt("general_interpretation").format(
                    gua_name=gua_info['name'],
                    gua_code=gua_info['code'],
                    question=question,
                    gua_ci=gua_info['gua_ci']
                )
            
            # 构建消息
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM服务进行流式处理
            await LLMService.process_streaming_chat(
                messages=messages,
                send_callback=self.send_streaming_data,
                message_type='divination_interpretation',
                max_tokens=1500,
                temperature=0.7
            )
            
        except Exception as e:
            error_message = f"执行流式卦象解读时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            await self.send_error(error_message)
    
    async def send_streaming_data(self, content, message_type, done=False):
        """
        发送流式数据的回调函数
        
        Args:
            content (str): 要发送的内容
            message_type (str): 消息类型
            done (bool): 是否完成
        """
        try:
            await self.send(text_data=json.dumps({
                'type': message_type,
                'content': content,
                'done': done
            }))
        except Exception as e:
            safe_print(f"发送流式数据时出错: {str(e)}")
    
    async def handle_chat_message(self, user_message):
        """
        处理普通聊天消息
        
        Args:
            user_message (str): 用户消息
        """
        try:
            safe_print(f"收到聊天消息: {user_message[:50]}...")
            
            # 确保消息是UTF-8编码的字符串
            if isinstance(user_message, bytes):
                user_message = user_message.decode('utf-8')
            
            # 使用简单消息格式
            messages = [
                {"role": "system", "content": get_divination_system_prompt("general")},
                {"role": "user", "content": user_message}
            ]
            
            # 调用LLM服务进行流式处理
            await LLMService.process_streaming_chat(
                messages=messages,
                send_callback=self.send_streaming_data,
                message_type='chat_response',
                max_tokens=1000,
                temperature=0.7
            )
            
        except Exception as e:
            error_message = f"处理聊天消息时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            await self.send_error(error_message) 