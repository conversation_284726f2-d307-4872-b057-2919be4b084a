# -*- coding: utf-8 -*-
"""
认证相关API - 使用Django Ninja异步实现
包含token刷新、微信登录、手机号登录等认证功能
"""

import json
import jwt
import logging
import traceback
import re
import time
import functools
import aiohttp
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Optional
import hmac
import hashlib
import base64

from django.conf import settings
from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
from ninja import Router, Schema
from pydantic import BaseModel, validator

from api.models.user_models import UserInfo, BannedAccount
from .async_utils import get_async, save_async, get_or_create_async
# 导入限流装饰器
from api.utils.rate_limiter import rate_limit, ip_rate_limit

logger = logging.getLogger(__name__)

# 并发监控
_concurrent_requests = 0
_max_concurrent = 0

def monitor_concurrency(func_name=None):
    """并发请求监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            global _concurrent_requests, _max_concurrent
            
            _concurrent_requests += 1
            if _concurrent_requests > _max_concurrent:
                _max_concurrent = _concurrent_requests
            
            name = func_name or func.__name__
            print(f"[CONCURRENT] 🔥 当前并发: {_concurrent_requests}, 峰值: {_max_concurrent} | {name}")
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                _concurrent_requests -= 1
                
        return wrapper
    return decorator

# 创建路由器
auth_router = Router()

def api_timer(func_name=None):
    """
    API耗时计算装饰器
    打印API执行时间，用于性能监控和优化
    
    Args:
        func_name: 自定义函数名，如果不提供则使用实际函数名
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            name = func_name or func.__name__
            print(f"[API_TIMER] 🚀 {name} 开始执行")
            
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")
                raise
                
        return wrapper
    return decorator

# 请求和响应模型
class RefreshTokenRequest(BaseModel):
    refresh_token: Optional[str] = None

class WeChatLoginRequest(BaseModel):
    code: str

class PhoneLoginRequest(BaseModel):
    phoneNumber: str
    # 添加来源验证字段
    source_token: Optional[str] = None  # 云函数签名token
    device_id: Optional[str] = None
    client_ip: Optional[str] = None
    
    @validator('phoneNumber')
    def validate_phone_number(cls, v):
        """验证手机号格式"""
        if not v:
            raise ValueError('手机号不能为空')
        
        # 中国大陆手机号格式验证
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('请输入正确的手机号格式')
        
        return v

class AuthTokenResponse(BaseModel):
    access_token: str
    refresh_token: str

class RefreshTokenResponse(BaseModel):
    access_token: str

class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None

class BanErrorResponse(BaseModel):
    error: str
    reason: Optional[str] = None
    detail: Optional[str] = None
    ban_time: Optional[str] = None
    ban_until: Optional[str] = None
    expire_at: Optional[str] = None

# Token刷新接口
@auth_router.post("/refresh-token", response={200: RefreshTokenResponse, 400: ErrorResponse, 466: ErrorResponse, 500: ErrorResponse})
@api_timer("Token刷新")
# @rate_limit("refresh_token_api", normal_limit=50, member_limit=200)  # Token刷新相对频繁，但需要控制
async def refresh_token_api(request, data: RefreshTokenRequest = None):
    """
    刷新访问令牌API
    支持从请求头Refresh-Token或请求体中获取refresh_token
    增加缓存机制，避免短时间内重复刷新
    """
    try:
        # 尝试从请求头中获取刷新令牌
        refresh_token = request.headers.get('Refresh-Token')

        if not refresh_token and data:
            # 如果请求头中没有，从请求体获取
            refresh_token = data.refresh_token

        if not refresh_token:
            logger.warning("refresh-token API调用缺少refresh_token")
            return 400, {"error": "缺少 refresh_token"}

        logger.info(f"收到refresh-token请求，token前缀: {refresh_token[:20]}...")

        # 🚀 新增：检查token刷新缓存，避免短时间内重复刷新
        refresh_cache_key = f"token_refresh:{refresh_token[:32]}"  # 使用token前32位作为缓存键
        cached_token = cache.get(refresh_cache_key)

        if cached_token:
            logger.info(f"Token刷新缓存命中，返回缓存的access_token")
            print(f"[CACHE_HIT] 🎯 Token刷新缓存命中，避免重复刷新 - 缓存键: {refresh_cache_key[:20]}...")
            return 200, {"access_token": cached_token}

        print(f"[CACHE_MISS] 💾 Token刷新缓存未命中，需要生成新token - 缓存键: {refresh_cache_key[:20]}...")

        try:
            # 验证刷新token
            payload = jwt.decode(refresh_token, settings.SECRET_KEY, algorithms=['HS256'])
            user_id = payload.get('user_id')

            if not user_id:
                logger.warning("refresh_token中缺少user_id")
                return 400, {"error": "无效的刷新token", "detail": "缺少用户ID"}

            # 使用异步方式获取用户信息
            user = await get_async(UserInfo, id=user_id)
            if not user:
                logger.warning(f"用户不存在: user_id={user_id}")
                return 400, {"error": "用户不存在"}

            # 生成新的访问token
            new_access_token = generate_access_token(user)

            # 🚀 新增：将新生成的access_token缓存5小时（与access_token过期时间一致）
            # 这样在access_token有效期内，不会重复刷新
            cache_timeout = 5 * 60 * 60  # 5小时，略短于access_token的6小时过期时间
            cache.set(refresh_cache_key, new_access_token, cache_timeout)
            print(f"[CACHE_SET] ✅ Token刷新结果已缓存 {cache_timeout}秒，避免重复刷新 - 缓存键: {refresh_cache_key[:20]}...")

            logger.info(f"成功刷新token，用户ID: {user_id}")
            return 200, {"access_token": new_access_token}

        except jwt.ExpiredSignatureError:
            logger.warning("刷新token已过期")
            return 466, {"error": "刷新token已过期"}
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的刷新token: {str(e)}")
            return 400, {"error": "无效的刷新token", "detail": str(e)}
        except Exception as e:
            logger.error(f"验证刷新token时发生错误: {str(e)}")
            return 500, {"error": "刷新token时发生错误", "detail": str(e)}

    except Exception as e:
        logger.error(f"处理refresh-token请求时发生未知错误: {str(e)}")
        return 500, {"error": "处理请求时发生错误", "detail": str(e)}

async def make_async_http_request(url: str, method: str = 'GET', **kwargs) -> dict:
    """
    简洁可靠的异步HTTP请求函数
    
    Args:
        url: 请求URL
        method: HTTP方法
        **kwargs: 额外的请求参数
    
    Returns:
        dict: 响应JSON数据或错误信息
    """
    logger.info(f"[HTTP] 请求微信API: {url}")
    
    try:
        # 使用独立的会话，确保稳定性
        timeout = aiohttp.ClientTimeout(total=5, connect=2)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.request(method, url, **kwargs) as response:
                # 获取响应文本
                response_text = await response.text()
                logger.info(f"[HTTP] 微信API原始响应: {response_text}")
                
                # 尝试解析JSON
                try:
                    response_data = json.loads(response_text)
                    return response_data
                except json.JSONDecodeError as e:
                    logger.warning(f"[HTTP] JSON解析失败: {e}, 响应内容: {response_text[:200]}")
                    return {"error": "响应格式错误", "raw_response": response_text}
                    
    except asyncio.TimeoutError:
        logger.error(f"[HTTP] 请求超时: {url}")
        return {"error": "请求超时"}
    except aiohttp.ClientError as e:
        logger.error(f"[HTTP] 客户端错误: {e}")
        return {"error": f"网络错误: {str(e)}"}
    except Exception as e:
        logger.error(f"[HTTP] 未知错误: {e}")
        return {"error": f"未知错误: {str(e)}"}

async def get_user_from_cache_or_db(openid: str, unionid: str = None) -> Optional[UserInfo]:
    """
    从缓存或数据库获取用户信息
    优先使用缓存，提升查询速度
    """
    cache_key = f"wechat_user:{unionid or openid}"
    
    # 尝试从缓存获取
    cached_user_id = cache.get(cache_key)
    if cached_user_id:
        user = await get_async(UserInfo, id=cached_user_id)
        if user:
            return user
    
    # 缓存未命中，从数据库查询
    user = None
    if unionid:
        user = await get_async(UserInfo, wx_unionid_new=unionid)
    
    if not user and openid:
        user = await get_async(UserInfo, wx_openid_new=openid)
    
    # 缓存用户ID（缓存30分钟）
    if user:
        cache.set(cache_key, user.id, 1800)
    
    return user

# 微信登录接口
@auth_router.post("/wechat-login", response={200: AuthTokenResponse, 400: ErrorResponse, 403: BanErrorResponse, 500: ErrorResponse})
@api_timer("微信登录")
@ip_rate_limit("wechat_login_api", limit_per_hour=30)  # 基于IP的限流，每小时30次
@monitor_concurrency("微信登录API")
async def wechat_login_api(request, data: WeChatLoginRequest):
    """
    微信一键注册登录API - 高性能优化版本
    返回access_token和refresh_token
    """
    try:
        step_start = time.time()
        code = data.code
        if not code:
            return 400, {"error": "缺少 code 参数"}

        # 构建微信API URL
        wx_login_url = f"https://api.weixin.qq.com/sns/oauth2/access_token?appid={settings.WX_APP_ID}&secret={settings.WX_APP_SECRET}&code={code}&grant_type=authorization_code"
        
        # 1. 异步调用微信API (性能监控)
        api_start = time.time()
        logger.info(f"调用微信API: {wx_login_url}")
        wx_response_data = await make_async_http_request(wx_login_url)
        api_duration = time.time() - api_start
        print(f"[PERF] 微信API调用耗时: {api_duration:.3f}秒")

        # 详细的错误日志
        logger.info(f"微信API原始响应: {wx_response_data}")
        
        if 'openid' not in wx_response_data:
            error_detail = wx_response_data.get('errmsg', wx_response_data.get('error', '微信API响应异常'))
            logger.error(f"微信登录失败 - 缺少openid: {wx_response_data}")
            return 400, {"error": "微信登录失败", "detail": error_detail}

        wx_openid_new = wx_response_data['openid']
        wx_unionid_new = wx_response_data.get('unionid')

        # 2. 并行执行封禁检查和用户查询 (性能监控)
        parallel_start = time.time()
        banned_task = asyncio.create_task(check_banned_async(
            phone=None, 
            openid=wx_openid_new,
            unionid=wx_unionid_new
        ))
        
        user_task = asyncio.create_task(get_user_from_cache_or_db(wx_openid_new, wx_unionid_new))
        
        # 等待并行任务完成
        is_banned, banned_info = await banned_task
        user = await user_task
        parallel_duration = time.time() - parallel_start
        print(f"[PERF] 并行查询(封禁+用户)耗时: {parallel_duration:.3f}秒")

        # 3. 快速封禁检查
        if is_banned:
            logger.warning(f"用户被封禁，openid: {wx_openid_new}, 原因: {banned_info.get_reason_display() if banned_info else '未知原因'}")
            return 403, {
                'error': '您的账号已被封禁',
                'reason': banned_info.get_reason_display() if banned_info else '未知原因',
                'detail': banned_info.detail if banned_info else '',
                'expire_at': banned_info.expire_at.isoformat() if banned_info and banned_info.expire_at else None
            }
        # 4. 用户处理逻辑优化
        user_logic_start = time.time()
        user_created = False
        if not user:
            # 创建新用户
            user = UserInfo(wx_openid_new=wx_openid_new, wx_unionid_new=wx_unionid_new)
            user_created = True
        else:
            # 检查现有用户封禁状态
            if user.is_banned:
                if user.ban_until and user.ban_until <= timezone.now():
                    # 封禁已过期，解除封禁
                    user.is_banned = False
                else:
                    return 403, {
                        'error': '您的账号已被封禁',
                        'reason': user.ban_reason,
                        'ban_time': user.ban_time.isoformat() if user.ban_time else None,
                        'ban_until': user.ban_until.isoformat() if user.ban_until else None
                    }

        # 5. 批量更新用户信息
        current_time = timezone.now()
        user.wx_session_key_new = wx_response_data.get('session_key')
        user.wx_unionid_new = wx_unionid_new
        user.wx_phone_new = wx_response_data.get('phone', '')
        user.wx_access_token = wx_response_data.get('access_token')
        user.wx_refresh_token = wx_response_data.get('refresh_token')
        user.wx_access_token_expires = current_time + timedelta(seconds=wx_response_data.get('expires_in', 7200))
        user.login_method = 'WECHAT'
        user.last_login = current_time
        user_logic_duration = time.time() - user_logic_start
        print(f"[PERF] 用户逻辑处理耗时: {user_logic_duration:.3f}秒")
        
        # 6. 异步保存用户（后台执行，不阻塞响应）
        save_start = time.time()
        
        # 7. 立即生成token（不等待数据库保存完成）
        token_start = time.time()
        access_token_task = asyncio.create_task(asyncio.to_thread(generate_wechat_access_token, user))
        refresh_token_task = asyncio.create_task(asyncio.to_thread(generate_refresh_token, user))
        
        access_token, refresh_token = await asyncio.gather(access_token_task, refresh_token_task)
        token_duration = time.time() - token_start
        print(f"[PERF] Token生成耗时: {token_duration:.3f}秒")

        # 8. 更新缓存（立即执行，因为很快）
        cache_start = time.time()
        cache_key = f"wechat_user:{wx_unionid_new or wx_openid_new}"
        cache.set(cache_key, user.id, 1800)  # 缓存30分钟
        cache_duration = time.time() - cache_start
        print(f"[PERF] 缓存更新耗时: {cache_duration:.3f}秒")

        # 9. 后台异步保存用户数据（不阻塞响应）
        asyncio.create_task(_save_user_async(user, save_start))

        total_duration = time.time() - step_start
        print(f"[PERF] 总处理耗时: {total_duration:.3f}秒")

        return 200, {
            'access_token': access_token,
            'refresh_token': refresh_token
        }

    except Exception as e:
        logger.error(f"微信登录错误: {str(e)}")
        return 500, {"error": "服务器错误", "detail": str(e)}

async def _save_user_async(user, save_start):
    """后台异步保存用户数据"""
    try:
        await save_async(user)
        save_duration = time.time() - save_start
        print(f"[PERF] 后台数据库保存耗时: {save_duration:.3f}秒")
    except Exception as e:
        logger.error(f"后台保存用户数据失败: {e}")
        # 这里可以添加重试逻辑或错误通知

# 手机号登录接口
@auth_router.post("/phone-login", response={200: AuthTokenResponse, 400: ErrorResponse, 403: BanErrorResponse, 500: ErrorResponse})
@api_timer("手机号登录")
@ip_rate_limit("phone_login_api", limit_per_hour=20)  # 基于IP的限流，每小时20次
async def phone_login_api(request, data: PhoneLoginRequest):
    """
    手机号一键注册登录API - 与云函数适配版本
    云函数已处理频率限制，此处专注于验证和业务逻辑
    """
    try:
        phone_number = data.phoneNumber
        device_id = data.device_id or 'unknown'
        client_ip = data.client_ip or request.META.get('REMOTE_ADDR', 'unknown')
        
        logger.info(f"收到手机号登录请求，手机号: {phone_number}, 设备ID: {device_id}, IP: {client_ip}")

        # 1. 验证来源token（云函数签名验证）
        if data.source_token:
            if not verify_source_token(data.source_token, phone_number):
                logger.warning(f"来源token验证失败: {phone_number}")
                return 400, {"error": "来源验证失败"}
        else:
            logger.warning(f"缺少来源token: {phone_number}")
            return 400, {"error": "缺少安全验证"}

        # 2. 检查是否为可疑的连续手机号
        if is_suspicious_phone_number(phone_number):
            logger.warning(f"检测到可疑手机号: {phone_number}")
            return 400, {"error": "手机号格式异常，请联系客服"}

        # 3. 检查手机号是否在封禁列表中
        is_banned, banned_info = await check_banned_async(
            phone=phone_number,
            openid=None,
            unionid=None
        )
        
        if is_banned:
            ban_info = {
                'error': '您的账号已被封禁',
                'reason': banned_info.get_reason_display() if banned_info else '未知原因',
                'detail': banned_info.detail if banned_info else '',
                'expire_at': banned_info.expire_at.isoformat() if banned_info and banned_info.expire_at else None
            }
            return 403, ban_info

        # 4. 使用异步方式获取或创建用户
        user, created = await get_or_create_async(UserInfo, wx_phone_new=phone_number)
        
        if created:
            logger.info('创建新用户')
            # 记录新用户创建
            await log_user_creation(phone_number, client_ip, device_id)
        else:
            logger.info('用户已存在')

        # 5. 更新用户信息
        user.last_login = timezone.now()
        user.login_method = 'PHONE'
        # 记录登录来源信息
        user.last_login_ip = client_ip
        user.last_device_id = device_id
        await save_async(user)
        
        logger.info(f'用户ID: {user.id}')

        # 6. 生成访问token和刷新token
        access_token = generate_phone_access_token(user)
        refresh_token = generate_refresh_token(user)

        response_data = {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
        
        logger.info(f"手机号登录成功，用户ID: {user.id}")
        return 200, response_data

    except ValueError as e:
        logger.warning(f"手机号格式验证失败: {str(e)}")
        return 400, {"error": str(e)}
    except Exception as e:
        logger.error(f"手机号登录发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        return 500, {"error": "服务器错误", "detail": str(e)}

# 安全工具函数
def verify_source_token(source_token: str, phone_number: str) -> bool:
    """验证云函数来源token的有效性"""
    try:
        # 解码base64
        decoded_data = base64.b64decode(source_token).decode('utf-8')
        token_data = json.loads(decoded_data)
        
        # 验证必要字段
        if not all(key in token_data for key in ['phone', 'timestamp', 'exp', 'signature']):
            return False
            
        # 验证手机号匹配
        if token_data.get('phone') != phone_number:
            return False
            
        # 验证时间戳（5分钟内有效）
        current_time = datetime.utcnow().timestamp()
        if current_time > token_data.get('exp', 0):
            return False
            
        # 验证签名
        payload = {
            'phone': token_data['phone'],
            'timestamp': token_data['timestamp'],
            'exp': token_data['exp']
        }
        message = json.dumps(payload, separators=(',', ':'))
        expected_signature = hmac.new(
            settings.PHONE_LOGIN_SECRET.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(expected_signature, token_data.get('signature', ''))
        
    except Exception as e:
        logger.error(f"验证来源token失败: {str(e)}")
        return False

def is_suspicious_phone_number(phone_number: str) -> bool:
    """检测可疑的连续手机号"""
    try:
        # 检查是否为连续数字
        digits = phone_number[3:]  # 去掉前缀
        
        # 检查是否为连续递增/递减
        consecutive_count = 0
        for i in range(len(digits) - 1):
            if int(digits[i+1]) == int(digits[i]) + 1 or int(digits[i+1]) == int(digits[i]) - 1:
                consecutive_count += 1
            else:
                consecutive_count = 0
            
            if consecutive_count >= 5:  # 连续6位数字
                return True
        
        # 检查重复数字
        for digit in '0123456789':
            if digits.count(digit) >= 6:
                return True
                
        return False
    except Exception:
        return False

async def log_user_creation(phone_number: str, client_ip: str, device_id: str):
    """记录新用户创建日志"""
    logger.info(f"新用户创建: 手机号={phone_number}, IP={client_ip}, 设备ID={device_id}")

# 异步工具函数
async def check_banned_async(phone=None, openid=None, unionid=None):
    """异步检查账号是否被封禁"""
    try:
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def _check_banned():
            return BannedAccount.check_banned(phone=phone, openid=openid, unionid=unionid)
        
        return await _check_banned()
    except Exception as e:
        logger.error(f"检查封禁状态时发生错误: {str(e)}")
        return False, None

# Token生成函数
def generate_access_token(user):
    """生成新的访问token"""
    payload = {
        'user_id': user.id,
        'exp': datetime.utcnow() + timedelta(hours=6)  # 6小时过期
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

def generate_wechat_access_token(user):
    """生成微信登录的访问token"""
    payload = {
        'user_id': user.id,
        'wx_openid': user.wx_openid_new,
        'wx_unionid': user.wx_unionid_new,
        'exp': datetime.utcnow() + timedelta(hours=6)  # 6小时过期
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

def generate_phone_access_token(user):
    """生成手机号登录的访问token"""
    payload = {
        'user_id': user.id,
        'phone_number': user.wx_phone_new,
        'exp': datetime.utcnow() + timedelta(hours=1)  # 1小时过期
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

def generate_refresh_token(user):
    """生成刷新token"""
    payload = {
        'user_id': user.id,
        'exp': datetime.utcnow() + timedelta(days=90)  # 90天过期
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256') 