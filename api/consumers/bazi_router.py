# api/consumers/bazi_router.py
"""
八字分析WebSocket路由管理器
支持多种分析类型的统一路由处理
"""

import json
import traceback
import time
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from volcenginesdkarkruntime import Ark
from django.conf import settings
from api.prompts import PromptManager, PromptCategory
import logging

logger = logging.getLogger(__name__)


class BaziAnalysisRouter(AsyncWebsocketConsumer):
    """
    八字分析路由器
    统一处理所有类型的八字分析请求
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = None
        self.conversation_history = []
        self.prompt_manager = PromptManager()
        self.current_category = None
        print('初始化BaziAnalysisRouter - 统一路由管理')
    
    async def connect(self):
        try:
            print("[BAZI_ROUTER] 🚀 WebSocket连接建立开始")
            
            # 初始化Ark客户端
            api_key = getattr(settings, 'ARK_API_KEY', None)
            if not api_key:
                raise ValueError("ARK_API_KEY未在settings中配置")
                
            self.client = Ark(
                api_key=api_key,
                base_url="https://ark.cn-beijing.volces.com/api/v3"
            )
            print("[BAZI_ROUTER] 🤖 Ark客户端初始化成功")
            
            await self.accept()
            print("[BAZI_ROUTER] ✅ WebSocket连接已接受")
            
        except Exception as e:
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"[BAZI_ROUTER] ❌ 连接失败详情: {error_details}")
            await self.close(code=4000)
    
    async def disconnect(self, close_code):
        """优化的断开连接处理"""
        try:
            print(f"[BAZI_ROUTER] 🔌 开始断开WebSocket连接，代码: {close_code}")

            # 清理Ark客户端连接
            if hasattr(self, 'client') and self.client:
                try:
                    # 如果有正在进行的请求，尝试取消
                    if hasattr(self.client, '_session'):
                        await self.client._session.close()
                except Exception as e:
                    print(f"[BAZI_ROUTER] ⚠️ 清理Ark客户端时出错: {e}")
                finally:
                    self.client = None

            # 清理对话历史
            if hasattr(self, 'conversation_history'):
                self.conversation_history.clear()

            # 清理其他资源
            self.current_category = None

            print(f"[BAZI_ROUTER] ✅ WebSocket连接断开完成，代码: {close_code}")

        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ 断开连接时出错: {e}")
        finally:
            # 确保调用父类的断开方法
            try:
                await super().disconnect(close_code)
            except Exception as e:
                print(f"[BAZI_ROUTER] ⚠️ 调用父类disconnect时出错: {e}")
    
    async def receive(self, text_data):
        """处理接收到的消息"""
        print(f"[BAZI_ROUTER] 🚀 开始处理消息")
        
        try:
            # 解析接收到的消息
            data = json.loads(text_data)
            
            # 获取分析类型
            analysis_type = data.get('type')
            if not analysis_type:
                await self.send_error('请指定分析类型')
                return
            
            # 验证分析类型是否支持
            if not self._is_valid_analysis_type(analysis_type):
                await self.send_error(f'不支持的分析类型: {analysis_type}')
                return
            
            # 处理八字分析请求
            if 'data' in data:
                print(f"[BAZI_ROUTER] 📨 收到{analysis_type}分析请求")
                await self._handle_bazi_analysis(analysis_type, data['data'])
            # 处理普通消息
            elif 'message' in data:
                user_message = data.get('message', '')
                print(f"[BAZI_ROUTER] 📨 收到普通消息: {user_message[:100]}...")
                await self._handle_general_message(user_message)
            else:
                await self.send_error('请发送八字分析数据或普通消息')
                
        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ 消息处理异常: {str(e)}")
            await self.send_error(f"处理消息时出错: {str(e)}")
    
    def _is_valid_analysis_type(self, analysis_type: str) -> bool:
        """验证分析类型是否有效"""
        valid_types = [
            'health', 'wealth', 'career', 'education', 'marriage', 'fortune',
            'wuxing', 'shishen', 'shensha', 'yearly', 'monthly', 'daily',
            'comprehensive', 'personality', 'relationship'
        ]
        return analysis_type in valid_types
    
    async def _handle_bazi_analysis(self, analysis_type: str, bazi_data: dict):
        """处理八字分析请求"""
        try:
            # 获取对应的提示词分类
            category = self._get_prompt_category(analysis_type)
            if not category:
                await self.send_error(f'未找到{analysis_type}对应的提示词')
                return

            self.current_category = category

            # 获取版本信息（可以从消息中指定，默认使用最新版本）
            version = bazi_data.get('version', self._get_latest_version(category))
            print(f"[BAZI_ROUTER] 📝 使用提示词版本: {category.value} {version}")

            # 获取系统提示词
            system_prompt = self.prompt_manager.get_system_prompt(category, version)
            
            # 重置对话历史，使用新的系统提示词
            self.conversation_history = [
                {"role": "system", "content": system_prompt}
            ]
            
            # 格式化用户消息
            user_message = self._format_bazi_message(analysis_type, bazi_data)
            print(f"[BAZI_ROUTER] 📝 格式化后的消息: {user_message[:200]}...")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            # 调用AI分析
            await self._call_ai_analysis()
            
        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ 八字分析失败: {str(e)}")
            await self.send_error(f"八字分析时出错: {str(e)}")
    
    async def _handle_general_message(self, user_message: str):
        """处理普通消息"""
        try:
            # 如果没有当前分类，使用综合分析
            if not self.current_category:
                self.current_category = PromptCategory.COMPREHENSIVE
                system_prompt = self.prompt_manager.get_system_prompt(self.current_category)
                self.conversation_history = [
                    {"role": "system", "content": system_prompt}
                ]
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            # 调用AI分析
            await self._call_ai_analysis()
            
        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ 处理普通消息失败: {str(e)}")
            await self.send_error(f"处理消息时出错: {str(e)}")
    
    async def _call_ai_analysis(self):
        """调用AI进行分析"""
        try:
            # 使用Ark客户端调用API
            response = self.client.chat.completions.create(
                model="ep-20250424123213-kk2fv",
                messages=self.conversation_history,
                stream=True
            )
            
            # 处理流式响应
            assistant_message = await self.send_stream_response(response)
            print(f"[BAZI_ROUTER] 📤 完整回复: {assistant_message[:200]}...")
            
        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ AI分析失败: {str(e)}")
            await self.send_error(f"AI分析时出错: {str(e)}")
    
    def _get_prompt_category(self, analysis_type: str) -> PromptCategory:
        """根据分析类型获取提示词分类"""
        type_mapping = {
            'health': PromptCategory.HEALTH,
            'wealth': PromptCategory.WEALTH,
            'career': PromptCategory.CAREER,
            'education': PromptCategory.EDUCATION,
            'marriage': PromptCategory.MARRIAGE,
            'fortune': PromptCategory.FORTUNE,
            'wuxing': PromptCategory.WUXING,
            'shishen': PromptCategory.SHISHEN,
            'shensha': PromptCategory.SHENSHA,
            'yearly': PromptCategory.YEARLY,
            'monthly': PromptCategory.MONTHLY,
            'daily': PromptCategory.DAILY,
            'comprehensive': PromptCategory.COMPREHENSIVE,
            'personality': PromptCategory.PERSONALITY,
            'relationship': PromptCategory.RELATIONSHIP,
        }
        return type_mapping.get(analysis_type)
    
    def _format_bazi_message(self, analysis_type: str, bazi_data: dict) -> str:
        """格式化八字分析消息"""
        try:
            eight_char = bazi_data.get('eight_char', '未知八字')
            gender = bazi_data.get('gender', '未知')
            shishen = bazi_data.get('shishen', {})
            shensha = bazi_data.get('shensha', {})
            
            # 获取分析类型的中文名称
            category = self._get_prompt_category(analysis_type)
            analysis_name = PromptCategory.get_display_name(category) if category else analysis_type
            
            message_parts = [
                f"请进行{analysis_name}分析，基于以下八字信息：",
                f"",
                f"八字：{eight_char}",
                f"性别：{gender}",
                f"",
            ]
            
            # 添加十神信息
            if shishen:
                message_parts.append("十神信息：")
                gan_shishen = shishen.get('gan_shishen', {})
                if gan_shishen:
                    message_parts.append(f"天干十神：{json.dumps(gan_shishen, ensure_ascii=False)}")
                
                zhi_shishen = shishen.get('zhi_shishen', {})
                if zhi_shishen:
                    message_parts.append(f"地支十神：{json.dumps(zhi_shishen, ensure_ascii=False)}")
                message_parts.append("")
            
            # 添加神煞信息
            if shensha:
                message_parts.append("神煞信息：")
                unique_shenshas = shensha.get('unique_shenshas', [])
                if unique_shenshas:
                    message_parts.append(f"神煞：{', '.join(unique_shenshas)}")
                
                total_count = shensha.get('total_count', 0)
                if total_count:
                    message_parts.append(f"神煞总数：{total_count}")
                message_parts.append("")
            
            message_parts.append(f"请重点从{analysis_name}角度进行深入分析。")
            
            return "\n".join(message_parts)
            
        except Exception as e:
            return f"八字信息解析出错，请重新发送。错误：{str(e)}"

    async def send_error(self, message: str, done: bool = True):
        """发送错误消息"""
        try:
            await self.send(text_data=json.dumps({
                'error': message,
                'done': done
            }))
        except Exception as e:
            print(f"发送错误消息失败: {str(e)}")

    async def send_stream_response(self, response):
        """优化的流式响应处理 - 增加连接状态检查"""
        assistant_message = ""
        chunk_count = 0
        connection_closed = False

        try:
            # 实时处理和发送每个数据块
            for chunk in response:
                # 检查连接是否已关闭
                if connection_closed:
                    print(f"[BAZI_ROUTER] 🔌 连接已关闭，停止处理流式响应")
                    break

                try:
                    if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content
                        if content:
                            assistant_message += content
                            chunk_count += 1

                            # 检查WebSocket连接状态
                            if hasattr(self, 'scope') and hasattr(self.scope, 'get'):
                                # 尝试发送数据，如果失败说明连接已断开
                                try:
                                    await self.send(text_data=json.dumps({
                                        'content': content,
                                        'done': False
                                    }))
                                except Exception as send_error:
                                    print(f"[BAZI_ROUTER] 🔌 检测到连接断开: {send_error}")
                                    connection_closed = True
                                    break

                            # 每处理20个chunk就让出控制权，避免长时间占用
                            if chunk_count % 20 == 0:
                                await asyncio.sleep(0.001)  # 短暂让出控制权

                except Exception as e:
                    # 如果是连接相关的错误，标记连接已关闭
                    if "closed" in str(e).lower() or "protocol" in str(e).lower():
                        print(f"[BAZI_ROUTER] 🔌 连接断开检测: {e}")
                        connection_closed = True
                        break
                    else:
                        print(f"[BAZI_ROUTER] ⚠️ 处理chunk时出错: {e}")
                        continue

            print(f"[BAZI_ROUTER] 📊 流式响应完成，共处理 {chunk_count} 个chunks，连接状态: {'已断开' if connection_closed else '正常'}")

        except Exception as e:
            print(f"[BAZI_ROUTER] ❌ 流式响应处理异常: {e}")
            connection_closed = True

        # 只有在连接正常时才发送完成标记
        if not connection_closed:
            try:
                await self.send(text_data=json.dumps({
                    'content': '',
                    'done': True
                }))
                print(f"[BAZI_ROUTER] ✅ 完成标记发送成功")
            except Exception as e:
                print(f"[BAZI_ROUTER] ⚠️ 发送完成标记时出错: {e}")

        # 将完整回复添加到历史记录
        if assistant_message:
            self.conversation_history.append({"role": "assistant", "content": assistant_message})

        return assistant_message

    def _get_latest_version(self, category: PromptCategory) -> str:
        """获取指定分类的最新版本"""
        try:
            available = self.prompt_manager.list_available_prompts()
            versions = available.get(category.value, ['v1'])

            # 简单的版本排序（假设格式为v1, v2, v3...）
            def version_key(v):
                try:
                    return int(v[1:]) if v.startswith('v') else 0
                except:
                    return 0

            latest = max(versions, key=version_key)
            print(f"[BAZI_ROUTER] 📋 {category.value}可用版本: {versions}, 最新版本: {latest}")
            return latest

        except Exception as e:
            print(f"[BAZI_ROUTER] ⚠️ 获取最新版本失败: {e}, 使用默认版本v1")
            return 'v1'
