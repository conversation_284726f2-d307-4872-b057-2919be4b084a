# AI Chat 模块 - AI聊天相关视图
from ..base.imports import *

@method_decorator(csrf_exempt, name='dispatch')
@cache_get_requests(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
class chat_with_deepseek(APIView):
    """
    DeepSeek AI聊天接口
    支持流式对话和普通对话两种模式
    """
    
    @api_timer("DeepSeek聊天")
    def post(self, request):
        try:
            data = json.loads(request.body)
            messages = data.get('messages', [])
            stream = data.get('stream', False)
            
            if not messages:
                return JsonResponse({'error': 'messages参数不能为空'}, status=400)

            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            if stream:
                def generate():
                    try:
                        response = client.chat.completions.create(
                            model="deepseek-chat",
                            messages=messages,
                            stream=True,
                            temperature=0.3  # 降低温度参数，提高回答稳定性
                        )
                        for chunk in response:
                            if chunk.choices[0].delta.content is not None:
                                yield f"data: {json.dumps({'content': chunk.choices[0].delta.content}, ensure_ascii=False)}\n\n"
                    except Exception as e:
                        yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"
                    finally:
                        # 确保流式响应结束后关闭连接
                        from django.db import connections
                        for conn in connections.all():
                            try:
                                conn.close()
                            except Exception:
                                pass
                
                response = StreamingHttpResponse(generate(), content_type='text/plain')
                response['Cache-Control'] = 'no-cache'
                return response
            else:
                try:
                    response = client.chat.completions.create(
                        model="deepseek-chat",
                        messages=messages,
                        temperature=0.3  # 降低温度参数，提高回答稳定性
                    )
                    return JsonResponse({
                        'response': response.choices[0].message.content
                    })
                finally:
                    # 确保请求结束后关闭连接
                    from django.db import connections
                    for conn in connections.all():
                        try:
                            conn.close()
                        except Exception:
                            pass
                
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("DeepSeek聊天-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['medium'])  # GET请求智能缓存30分钟
    def get(self, request):
        """获取聊天配置和状态信息"""
        try:
            return JsonResponse({
                'status': 'available',
                'model': 'deepseek-chat',
                'features': ['text_chat', 'streaming'],
                'limits': {
                    'max_tokens': 4000,
                    'requests_per_minute': 30
                }
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass

@method_decorator(csrf_exempt, name='dispatch') 
@cache_get_requests(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
class chat_with_douban(APIView):
    """
    豆瓣AI聊天接口
    兼容原有的聊天功能
    """
    
    @api_timer("豆瓣聊天")
    def post(self, request):
        try:
            data = json.loads(request.body)
            messages = data.get('messages', [])
            
            if not messages:
                return JsonResponse({'error': 'messages参数不能为空'}, status=400)

            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            try:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=messages,
                    temperature=0.3  # 降低温度参数，提高回答稳定性
                )
                
                return JsonResponse({
                    'response': response.choices[0].message.content,
                    'model': 'deepseek-via-douban',
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                # 确保请求结束后关闭连接
                from django.db import connections
                for conn in connections.all():
                    try:
                        conn.close()
                    except Exception:
                        pass
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("豆瓣聊天-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['medium'])  # GET请求智能缓存30分钟
    def get(self, request):
        """获取豆瓣聊天配置信息"""
        try:
            return JsonResponse({
                'status': 'available',
                'model': 'douban-chat',
                'description': '豆瓣风格的AI聊天接口',
                'features': ['text_chat', 'context_aware']
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass 