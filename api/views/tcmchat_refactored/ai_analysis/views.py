# AI Analysis 视图 - 处理AI分析相关的视图
from ..base.imports import *

@method_decorator(csrf_exempt, name='dispatch')
@cache_get_requests(timeout=CACHE_TIMEOUT['medium'])  # GET请求缓存30分钟
class analysis_with_deepseek(APIView):
    """
    DeepSeek AI分析接口
    提供中医健康分析功能
    """
    
    @api_timer("DeepSeek分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            message = data.get('message', '')
            
            if not message:
                return JsonResponse({'error': 'message参数不能为空'}, status=400)
                
            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            system_prompt = "你是日月有数智能中医健康助手，专门进行中医健康分析。请根据用户提供的信息进行专业的中医分析，包括体质辨识、症状分析、调理建议等。"
            
            try:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": message}
                    ],
                    temperature=0.3  # 降低温度参数，提高中医分析稳定性
                )
                
                return JsonResponse({
                    'analysis': response.choices[0].message.content,
                    'model': 'deepseek-analysis',
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                # 确保请求结束后关闭数据库连接
                from django.db import connections
                for conn in connections.all():
                    try:
                        conn.close()
                    except Exception:
                        pass
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("DeepSeek分析-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['long'])  # GET请求智能缓存1小时
    def get(self, request):
        """获取分析服务配置信息"""
        try:
            return JsonResponse({
                'status': 'available',
                'service': 'deepseek-analysis',
                'features': ['constitution_analysis', 'symptom_analysis', 'health_advice'],
                'description': '基于DeepSeek的中医健康分析服务'
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass

@method_decorator(csrf_exempt, name='dispatch')
@cache_get_requests(timeout=CACHE_TIMEOUT['medium'])  # GET请求缓存30分钟
class analysis_with_deepseek_symptoms(APIView):
    """
    DeepSeek症状分析接口
    根据症状提供中医分析
    """
    
    @api_timer("DeepSeek症状分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            symptoms = data.get('symptoms', [])
            
            if not symptoms:
                return JsonResponse({'error': 'symptoms参数不能为空'}, status=400)
                
            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            symptoms_text = ", ".join(symptoms)
            prompt = f"根据以下症状进行中医分析：{symptoms_text}。请提供可能的证型、病因分析和调理建议。"
            
            try:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "system", "content": "你是专业的中医诊断助手，请根据症状进行辨证分析。"},
                        {"role": "user", "content": prompt}
                    ],
                )
                
                return JsonResponse({
                    'symptoms': symptoms,
                    'analysis': response.choices[0].message.content,
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                # 确保请求结束后关闭数据库连接
                from django.db import connections
                for conn in connections.all():
                    try:
                        conn.close()
                    except Exception:
                        pass
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("症状分析配置-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['long'])  # GET请求智能缓存1小时
    def get(self, request):
        """获取症状分析配置"""
        try:
            return JsonResponse({
                'status': 'available',
                'service': 'symptom-analysis',
                'supported_symptoms': ['头痛', '失眠', '疲劳', '消化不良', '情绪低落'],
                'analysis_types': ['证型分析', '病因分析', '调理建议']
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass

@method_decorator(csrf_exempt, name='dispatch')
class Only_analysis_with_deepseek_symptoms(APIView):
    """
    纯症状分析接口
    仅进行症状分析，不包含其他功能
    """
    
    @api_timer("纯症状分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            symptoms_data = data.get('symptoms_data', {})
            
            if not symptoms_data:
                return JsonResponse({'error': '症状数据不能为空'}, status=400)
                
            # 处理症状数据逻辑
            analysis_result = self._analyze_symptoms_only(symptoms_data)
            
            return JsonResponse({
                'pure_analysis': analysis_result,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保请求结束后关闭数据库连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
    
    @api_timer("纯症状分析-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
    def get(self, request):
        """获取纯症状分析服务信息"""
        try:
            return JsonResponse({
                'service': 'pure-symptom-analysis',
                'description': '纯症状分析服务，专注于症状数据分析',
                'methods': ['POST'],
                'parameters': {
                    'symptoms_data': '症状数据对象'
                },
                'features': ['快速分析', '纯症状处理', '简化结果']
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
    
    def _analyze_symptoms_only(self, symptoms_data):
        """纯症状分析逻辑"""
        # 简化的分析逻辑，实际应用中应该更复杂
        return {
            'severity_level': 'moderate',
            'primary_concerns': ['symptom1', 'symptom2'],
            'recommendations': ['建议1', '建议2']
        }

@method_decorator(csrf_exempt, name='dispatch') 
class analysis_with_deepseek_For_Now_advice(APIView):
    """
    即时建议分析接口
    提供快速的健康建议
    """
    
    @api_timer("即时建议分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            current_state = data.get('current_state', '')
            
            if not current_state:
                return JsonResponse({'error': '当前状态信息不能为空'}, status=400)
                
            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            prompt = f"基于当前状态：{current_state}，请提供即时的中医养生建议，要简洁实用。"
            
            try:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "system", "content": "你是中医养生专家，专门提供即时的养生建议。"},
                        {"role": "user", "content": prompt}
                    ],
                )
                
                return JsonResponse({
                    'immediate_advice': response.choices[0].message.content,
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                # 确保请求结束后关闭数据库连接
                from django.db import connections
                for conn in connections.all():
                    try:
                        conn.close()
                    except Exception:
                        pass
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("即时建议分析-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
    def get(self, request):
        """获取即时建议分析服务信息"""
        try:
            return JsonResponse({
                'service': 'immediate-advice',
                'description': '即时健康建议服务，提供快速中医养生建议',
                'methods': ['POST'],
                'parameters': {
                    'current_state': '当前状态描述'
                },
                'features': ['即时响应', '中医养生', '简洁实用']
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass

@method_decorator(csrf_exempt, name='dispatch')
class analysis_with_douban_symptoms(APIView):
    """
    豆瓣症状分析接口
    兼容豆瓣风格的症状分析
    """
    
    @api_timer("豆瓣症状分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            symptoms = data.get('symptoms', [])
            
            if not symptoms:
                return JsonResponse({'error': 'symptoms参数不能为空'}, status=400)
                
            client = OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url="https://api.deepseek.com",
            )
            
            symptoms_text = ", ".join(symptoms)
            prompt = f"以豆瓣风格分析这些症状：{symptoms_text}。要文艺一点，温和一点。"
            
            try:
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "system", "content": "你是温和文艺的健康顾问，用豆瓣风格回答问题。"},
                        {"role": "user", "content": prompt}
                    ],
                )
                
                return JsonResponse({
                    'douban_style_analysis': response.choices[0].message.content,
                    'symptoms': symptoms,
                    'style': 'douban',
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                # 确保请求结束后关闭数据库连接
                from django.db import connections
                for conn in connections.all():
                    try:
                        conn.close()
                    except Exception:
                        pass
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("豆瓣症状分析-GET")
    @smart_cache(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
    def get(self, request):
        """获取豆瓣风格症状分析服务信息"""
        try:
            return JsonResponse({
                'service': 'douban-style-analysis',
                'description': '豆瓣风格症状分析，文艺温和的健康分析',
                'methods': ['POST'],
                'parameters': {
                    'symptoms': '症状数组'
                },
                'style': 'douban',
                'features': ['文艺风格', '温和分析', '人文关怀']
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass

@method_decorator(csrf_exempt, name='dispatch')
class StreamingDeepseekAnalysisView(APIView):
    """
    流式DeepSeek分析视图
    支持实时流式分析响应
    """
    
    @api_timer("流式分析")
    def post(self, request):
        try:
            data = json.loads(request.body)
            content = data.get('content', '')
            
            if not content:
                return JsonResponse({'error': '分析内容不能为空'}, status=400)
                
            def generate_analysis():
                try:
                    client = OpenAI(
                        api_key=DEEPSEEK_API_KEY,
                        base_url="https://api.deepseek.com",
                    )
                    
                    response = client.chat.completions.create(
                        model="deepseek-chat",
                        messages=[
                            {"role": "system", "content": "你是专业的中医分析师，请进行详细分析。"},
                            {"role": "user", "content": content}
                        ],
                        stream=True,
                    )
                    
                    for chunk in response:
                        if chunk.choices[0].delta.content is not None:
                            yield f"data: {json.dumps({'content': chunk.choices[0].delta.content}, ensure_ascii=False)}\n\n"
                            
                except Exception as e:
                    yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"
                finally:
                    # 确保流式响应结束后关闭连接
                    from django.db import connections
                    for conn in connections.all():
                        try:
                            conn.close()
                        except Exception:
                            pass
            
            response = StreamingHttpResponse(generate_analysis(), content_type='text/plain')
            response['Cache-Control'] = 'no-cache'
            return response
            
        except Exception as e:
            # 确保异常时也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            return JsonResponse({'error': str(e)}, status=500)
    
    @api_timer("流式分析-GET")  
    @smart_cache(timeout=CACHE_TIMEOUT['short'])  # GET请求缓存5分钟
    def get(self, request):
        """获取流式分析服务信息"""
        try:
            messages = request.GET.get("messages")
            if messages:
                messages = json.loads(messages)
                print("messages---", messages)
                
                def generate_events(messages_list):
                    try:
                        client = OpenAI(
                            api_key=DEEPSEEK_API_KEY,
                            base_url="https://api.deepseek.com",
                        )
                        
                        response = client.chat.completions.create(
                            model="deepseek-chat",
                            messages=messages_list,
                            stream=True
                        )
                        
                        for chunk in response:
                            if chunk.choices[0].delta.content is not None:
                                content = chunk.choices[0].delta.content
                                # 使用SSE格式
                                yield f"data: {content}\n\n"
                    except Exception as e:
                        yield f"data: Error: {str(e)}\n\n"
                    finally:
                        # 确保流式响应结束后关闭连接
                        from django.db import connections
                        for conn in connections.all():
                            try:
                                conn.close()
                            except Exception:
                                pass
                
                response = StreamingHttpResponse(
                    generate_events(messages),
                    content_type='text/event-stream'
                )
                response['Cache-Control'] = 'no-cache'
                response['X-Accel-Buffering'] = 'no'
                return response
            else:
                return JsonResponse({
                    'service': 'streaming-analysis',
                    'description': '流式分析服务，支持实时流式响应',
                    'methods': ['POST', 'GET'],
                    'parameters': {
                        'content': '待分析内容（POST）',
                        'messages': '消息列表（GET）'
                    },
                    'features': ['实时流式', '中医分析', 'SSE支持']
                })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        finally:
            # 确保GET请求也关闭连接
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass 