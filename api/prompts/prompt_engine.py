# api/prompts/prompt_engine.py
"""
八字分析提示词工程
支持前端参数排列组合的动态提示词生成
"""

import json
import logging
from typing import Dict, Optional, Tuple
from .categories import AnalysisDirection, Domain, TimeScope

logger = logging.getLogger(__name__)


class BaziPromptEngine:
    """八字分析提示词工程"""
    
    def __init__(self):
        self.base_system_prompt = self._get_base_system_prompt()
        self.analysis_templates = self._load_analysis_templates()
        self.domain_templates = self._load_domain_templates()
        self.time_templates = self._load_time_templates()
    
    def generate_prompt(self, analysis_direction: str, domain: str,
                       time_scope: str, user_question: str = None) -> Tuple[str, str]:
        """
        根据前端参数生成提示词

        Args:
            analysis_direction: 分析方向
            domain: 分析领域
            time_scope: 时间范围
            user_question: 用户问题（仅决策支持类型需要）

        Returns:
            Tuple[str, str]: (系统提示词, 简化的用户提示词模板)
        """
        try:
            # 验证参数
            if not self._validate_parameters(analysis_direction, domain, time_scope):
                raise ValueError("参数验证失败")

            # 生成系统提示词（包含所有分析指导）
            system_prompt = self._build_system_prompt(analysis_direction, domain, time_scope, user_question)

            # 简化的用户提示词模板（只包含八字数据结构）
            user_prompt_template = self._get_simple_user_template()

            logger.info(f"生成提示词成功: {analysis_direction}_{domain}_{time_scope}")
            return system_prompt, user_prompt_template

        except Exception as e:
            logger.error(f"生成提示词失败: {e}")
            # 返回默认提示词
            return self._get_default_prompts()
    
    def _validate_parameters(self, analysis_direction: str, domain: str, time_scope: str) -> bool:
        """验证参数有效性"""
        try:
            AnalysisDirection(analysis_direction)
            Domain(domain)
            TimeScope(time_scope)
            return True
        except ValueError:
            return False
    
    def _build_system_prompt(self, analysis_direction: str, domain: str, time_scope: str, user_question: str = None) -> str:
        """构建简洁的系统提示词"""
        # 基础系统提示
        prompt_parts = [self.base_system_prompt.strip()]

        # 只添加最核心的分析指导，避免冗余
        if analysis_direction == "comprehensive_pattern":
            prompt_parts.append("请进行综合格局分析，包括格局层次、五行配置、十神组合、运势走向等方面。")
        elif domain == "health":
            prompt_parts.append("请重点分析体质特征，提供健康养生建议。")
        elif domain == "wealth":
            prompt_parts.append("请重点分析财运特征，提供理财建议。")
        elif domain == "career":
            prompt_parts.append("请重点分析事业发展，提供职业规划建议。")
        elif domain == "marriage":
            prompt_parts.append("请重点分析感情特征，提供婚恋建议。")
        elif analysis_direction == "decision_support" and user_question:
            prompt_parts.append(f"请针对用户问题提供决策建议：{user_question}")

        return "\n\n".join(prompt_parts)
    
    def _get_simple_user_template(self) -> str:
        """获取简化的用户提示词模板（只包含八字数据结构）"""
        return """八字：{eight_char}
性别：{gender}
出生日期：{birth_date}"""
    
    def _get_base_system_prompt(self) -> str:
        """获取核心分析框架的基础系统提示词"""
        return """你是专业的八字命理分析师，必须按照以下框架进行深度分析：

【分析框架】
1. 十神分析：以天干十神为主导，地支十神为辅助，分析十神力量对比和组合效应
2. 格局判断：综合日主强弱、用神忌神、十神配置，量化分析最可能的格局类型及成格概率（如正官格85%、伤官格60%等）
3. 神煞定位：重点分析神煞在四柱中的具体位置和作用力，量化其对格局的影响程度
4. 概率思维：避免绝对化断言，用概率和程度来表达分析结果（如"有70%可能"、"中等程度"等）

【量化要求】
- 格局成立概率必须量化（如：正财格成立概率75%）
- 五行强弱用数值表达（如：木旺30%、火弱15%）
- 神煞影响力分级（强/中/弱，并说明具体影响程度）

"""
    
    def _load_analysis_templates(self) -> Dict[str, str]:
        """加载分析方向模板"""
        return {
            "basic_analysis": """进行全面的基础八字分析，包括以下三个核心方面：
1. 五行强弱分析 - 评估五行力量分布和平衡状态
2. 十神特征解读 - 分析十神配置对性格的影响
3. 基本性格特点 - 总结个人特质和行为倾向""",

            "contradiction_analysis": """重点识别和分析八字中的冲突矛盾，包括以下三个方面：
1. 五行相克关系 - 分析五行间的冲突和制约
2. 十神冲突分析 - 识别十神间的矛盾和对立
3. 化解建议 - 提供调和冲突的具体方法""",

            "pattern_analysis": """深入分析八字格局模式，包括以下三个维度：
1. 格局类型判断 - 确定八字所属的格局类型
2. 用神喜忌分析 - 分析用神和忌神的作用
3. 运势走向预测 - 预测未来运势发展趋势""",

            "comprehensive_pattern": """进行最全面的综合格局分析，包括以下六个核心维度：
1. 整体格局评估和层次判断 - 分析八字整体结构和格局高低
2. 五行配置和强弱分析 - 评估五行力量分布和平衡状态
3. 十神组合和性格特征 - 解读十神配置对性格的影响
4. 神煞影响和特殊标志 - 分析重要神煞的作用和意义
5. 大运流年和人生阶段 - 预测运势变化和人生节点
6. 综合建议和发展方向 - 提供个性化的人生指导建议""",

            "decision_support": """针对用户的具体问题和困惑，提供决策建议，包括以下方面：
1. 问题分析 - 深入理解用户面临的具体问题
2. 八字角度的建议 - 结合命理特征提供指导
3. 最佳时机选择 - 分析行动的有利时间点"""
        }
    
    def _load_domain_templates(self) -> Dict[str, str]:
        """加载领域模板"""
        return {
            "health": "结合中医理论和五行养生，分析体质特征，提供健康调理建议。",
            "wealth": "分析财运特征和财富积累能力，提供理财投资的时机建议。",
            "education": "分析学习天赋和考试运势，提供学业规划和考试时机建议。",
            "career": "分析事业发展潜力和职场特征，提供职业规划和发展建议。",
            "marriage": "分析感情特征和婚恋运势，提供情感发展和配偶选择建议。",
            "fortune": "分析吉运特征和机遇把握能力，提供趋吉避凶的建议。",
            "misfortune": "分析潜在风险和化解方法，提供规避凶险的策略建议。",
            "overall_pattern": "从整体格局角度进行全方位分析，涵盖命理结构、性格特征、人生走向等各个层面，提供完整的命理解读。"
        }
    
    def _load_time_templates(self) -> Dict[str, str]:
        """加载时间范围模板"""
        return {
            "yearly": "重点分析年度大运走势，结合流年干支进行运势预测。",
            "monthly": "重点分析月度运势变化，结合月令特征进行精准分析。",
            "yearly_monthly": "综合分析年运和月运的相互影响，提供全面的时间运势指导。",
            "daily": "重点分析近期日常运势，提供短期内的行动建议。",
            "short_term": "分析近期运势发展，提供3-6个月内的规划建议。",
            "long_term": "分析长期运势趋势，提供1-3年的发展规划建议。",
            "lifetime": "从人生全局角度进行分析，涵盖各个人生阶段的发展特征，提供终生的命理指导和人生规划建议。"
        }
    
    def _get_default_prompts(self) -> Tuple[str, str]:
        """获取默认提示词"""
        system_prompt = self.base_system_prompt
        user_prompt = self._get_simple_user_template()

        return system_prompt, user_prompt


# 全局实例
prompt_engine = BaziPromptEngine()
