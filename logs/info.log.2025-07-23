INFO 2025-07-23 00:18:43,743 rate_limiter 1627969 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 91
INFO 2025-07-23 00:18:43,752 rate_limiter 1627969 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 92
INFO 2025-07-23 00:18:47,239 rate_limiter 1627969 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 93
INFO 2025-07-23 01:14:38,706 autoreload 1672680 *************** Watching for file changes with StatReloader
INFO 2025-07-23 01:14:41,814 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:14:41,817 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:14:41,847 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 94
INFO 2025-07-23 01:14:41,852 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 95
INFO 2025-07-23 01:14:41,857 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:14:41,862 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:14:42,590 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 96
INFO 2025-07-23 01:14:42,601 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:14:42,603 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:14:42,622 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:14:42,643 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
WARNING 2025-07-23 01:14:42,837 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:14:42,913 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:16:29,200 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:16:29,219 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 97
INFO 2025-07-23 01:16:29,230 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:16:29,248 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:16:29,256 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 98
INFO 2025-07-23 01:16:29,267 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:16:32,179 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:18:03,692 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:18:34,495 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:18:52,791 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:19:09,518 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:19:27,527 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:20:08,077 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:20:25,494 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:22:31,696 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:22:31,746 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:22:31,750 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 99
INFO 2025-07-23 01:22:31,764 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:22:31,768 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:22:31,790 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 100
INFO 2025-07-23 01:22:31,800 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:22:33,099 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:22:33,115 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:22:33,126 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:22:33,171 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:22:33,194 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 101
INFO 2025-07-23 01:22:33,204 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:22:33,337 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:22:33,337 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:22:33,418 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:22:33,479 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
WARNING 2025-07-23 01:22:33,547 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:22:43,154 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:22:43,158 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 102
INFO 2025-07-23 01:22:43,160 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:22:43,169 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:22:43,174 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:22:43,210 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
WARNING 2025-07-23 01:22:43,446 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:23:05,890 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:23:05,895 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 103
INFO 2025-07-23 01:23:05,907 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:23:05,910 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:23:05,918 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:23:05,937 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
WARNING 2025-07-23 01:23:06,182 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:25:38,447 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:25:38,498 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 104
INFO 2025-07-23 01:25:38,515 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:25:38,515 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:25:38,518 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:25:38,520 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:25:38,522 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:25:38,537 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:25:38,557 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
WARNING 2025-07-23 01:25:38,833 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:25:38,864 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:26:01,016 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:26:01,028 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:26:01,037 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:26:01,072 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:26:01,084 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 105
INFO 2025-07-23 01:26:01,094 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:26:01,214 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:26:01,215 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:26:01,257 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:26:01,376 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
WARNING 2025-07-23 01:26:01,474 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:26:27,563 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:26:27,572 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:26:27,589 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:26:27,652 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 106
INFO 2025-07-23 01:26:27,662 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:26:27,672 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:26:27,792 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:26:27,802 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:26:27,834 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:26:27,867 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
WARNING 2025-07-23 01:26:27,993 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:27:16,211 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:16,216 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:27:16,218 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:16,294 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 107
INFO 2025-07-23 01:27:16,303 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:16,304 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:27:16,365 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:27:16,427 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:27:16,467 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:16,597 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
WARNING 2025-07-23 01:27:16,622 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:27:32,714 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:27:32,714 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:32,725 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:32,772 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:32,773 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 108
INFO 2025-07-23 01:27:32,783 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:27:32,934 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:32,953 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:27:32,982 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:27:33,086 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
WARNING 2025-07-23 01:27:33,149 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:27:48,377 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:48,379 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:27:48,426 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:48,467 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:27:48,472 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 109
INFO 2025-07-23 01:27:48,482 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:27:48,589 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:27:48,629 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:27:48,651 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:27:48,786 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
WARNING 2025-07-23 01:27:48,855 basehttp 1672680 *************** "POST /api/bank/CustomActivityView/ HTTP/1.0" 404 179
INFO 2025-07-23 01:28:42,561 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:28:42,574 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:28:42,578 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:28:42,605 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:28:42,649 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 110
INFO 2025-07-23 01:28:42,659 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:28:42,750 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:28:42,776 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:28:42,791 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:28:42,793 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:32:59,218 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:32:59,228 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 111
INFO 2025-07-23 01:32:59,238 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:32:59,296 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:32:59,311 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:32:59,496 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:35:43,216 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 112
INFO 2025-07-23 01:35:43,226 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:35:43,279 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:35:43,279 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:35:43,281 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:35:43,333 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:36:13,536 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 200 371
INFO 2025-07-23 01:36:13,775 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 200 371
INFO 2025-07-23 01:36:14,006 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:36:25,445 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:36:25,520 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:36:25,556 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 113
INFO 2025-07-23 01:36:25,570 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:36:25,582 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:36:25,670 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 114
INFO 2025-07-23 01:36:25,680 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:36:26,372 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 115
INFO 2025-07-23 01:36:26,380 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:36:26,382 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:36:26,893 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 116
INFO 2025-07-23 01:36:26,896 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:36:26,904 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:36:26,910 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:36:26,977 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:36:27,010 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:37:43,928 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:37:43,928 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:37:43,950 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 117
INFO 2025-07-23 01:37:43,962 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:37:43,967 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 01:37:43,986 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 118
INFO 2025-07-23 01:37:43,996 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:37:45,786 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:37:45,821 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:37:45,843 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:37:45,916 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 119
INFO 2025-07-23 01:37:45,925 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:37:45,926 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:37:45,961 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:37:45,980 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:37:46,123 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:37:46,136 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:37:47,817 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 120
INFO 2025-07-23 01:37:47,827 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:37:47,854 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:37:47,861 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:37:47,905 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:37:47,940 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
WARNING 2025-07-23 01:38:28,146 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
WARNING 2025-07-23 01:38:28,405 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
WARNING 2025-07-23 01:38:41,984 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
WARNING 2025-07-23 01:38:42,231 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
INFO 2025-07-23 01:40:50,910 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:40:50,930 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:40:50,949 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:40:50,978 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:40:50,994 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:40:51,012 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 121
INFO 2025-07-23 01:40:51,027 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:40:51,042 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:40:51,183 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:40:51,194 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:41:37,243 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:41:37,281 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:41:37,300 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:41:37,333 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 122
INFO 2025-07-23 01:41:37,344 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:41:37,356 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:41:37,425 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:41:37,509 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:41:37,562 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:41:37,591 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:44:52,654 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:44:52,660 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:44:52,672 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:44:52,683 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 123
INFO 2025-07-23 01:44:52,692 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:44:52,694 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:44:52,759 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:44:52,771 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:44:52,792 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:44:52,947 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:45:18,427 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:45:18,427 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:45:18,433 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:45:18,492 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 124
INFO 2025-07-23 01:45:18,504 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:45:18,504 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:45:18,673 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:45:18,674 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:45:18,730 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:45:18,816 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:46:31,483 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:46:31,506 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:46:31,513 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:46:31,526 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:46:31,534 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 125
INFO 2025-07-23 01:46:31,545 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:46:31,611 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:46:31,621 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:46:31,704 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:46:31,724 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
ERROR 2025-07-23 01:46:35,678 exception 1672680 *************** Invalid HTTP_HOST header: 'apigateway.bbscrm.com'. You may need to add 'apigateway.bbscrm.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'apigateway.bbscrm.com'. You may need to add 'apigateway.bbscrm.com' to ALLOWED_HOSTS.
WARNING 2025-07-23 01:46:35,679 basehttp 1672680 *************** "GET /backend/api/ChannelApplet/QueryLoginPageInfo?customerCode=2149 HTTP/1.0" 400 143
WARNING 2025-07-23 01:47:29,364 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
WARNING 2025-07-23 01:47:29,628 basehttp 1672680 *************** "POST /api/bank/CreateCustomActivityView/ HTTP/1.0" 400 89
INFO 2025-07-23 01:47:29,930 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1451
INFO 2025-07-23 01:51:31,581 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:51:31,582 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:51:31,608 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:51:31,677 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 126
INFO 2025-07-23 01:51:31,681 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:51:31,687 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:51:31,689 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:51:31,765 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:51:31,824 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:51:31,890 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:52:24,965 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:52:24,965 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:52:24,965 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:52:25,030 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 127
INFO 2025-07-23 01:52:25,039 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:52:25,040 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:52:25,159 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:52:25,192 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:52:25,229 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:52:25,325 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 01:52:47,246 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:52:47,248 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 01:52:47,255 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:52:47,322 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 128
INFO 2025-07-23 01:52:47,333 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 01:52:47,342 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 01:52:47,517 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 01:52:47,583 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 01:52:47,583 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 01:52:47,628 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 02:00:56,363 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 02:00:56,391 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:00:56,397 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:00:56,411 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:00:56,423 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 129
INFO 2025-07-23 02:00:56,434 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:00:56,443 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:00:56,452 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 02:00:56,532 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 02:00:56,652 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 02:01:46,696 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:01:46,700 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 02:01:46,701 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:01:46,750 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:01:46,761 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 130
INFO 2025-07-23 02:01:46,771 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:01:46,922 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 02:01:46,938 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:01:46,959 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 02:01:46,972 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 02:02:15,309 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:02:15,320 basehttp 1672680 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-23 02:02:15,334 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:02:15,366 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:02:15,373 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 131
INFO 2025-07-23 02:02:15,382 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:02:15,579 basehttp 1672680 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-23 02:02:15,588 basehttp 1672680 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-23 02:02:15,613 basehttp 1672680 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-23 02:02:15,620 basehttp 1672680 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-23 02:05:28,107 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:05:28,119 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:05:28,142 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 132
INFO 2025-07-23 02:05:28,152 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:05:28,167 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:05:28,209 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 133
INFO 2025-07-23 02:05:28,219 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:06:52,776 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:06:52,776 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:06:52,822 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 134
INFO 2025-07-23 02:06:52,835 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:06:52,836 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:06:52,855 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 135
INFO 2025-07-23 02:06:52,865 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:07:05,988 basehttp 1672680 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-23 02:07:48,044 basehttp 1672680 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-23 02:07:48,350 basehttp 1672680 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
WARNING 2025-07-23 02:07:48,564 basehttp 1672680 *************** "GET /favicon.ico HTTP/1.0" 409 49
INFO 2025-07-23 02:09:36,144 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:36,163 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:36,181 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 136
INFO 2025-07-23 02:09:36,191 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 137
INFO 2025-07-23 02:09:36,193 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:09:36,205 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:09:36,213 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:37,751 basehttp 1672680 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-23 02:09:39,807 basehttp 1672680 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-23 02:09:41,851 basehttp 1672680 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-23 02:09:43,894 basehttp 1672680 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-23 02:09:53,193 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:53,193 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:53,221 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:09:53,221 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 138
INFO 2025-07-23 02:09:53,232 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:09:53,264 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 139
INFO 2025-07-23 02:09:53,274 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:10:11,841 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:10:11,849 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:10:11,870 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 140
INFO 2025-07-23 02:10:11,876 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:10:11,880 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:10:11,897 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 141
INFO 2025-07-23 02:10:11,907 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:11:29,667 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:11:29,689 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:11:29,748 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 142
INFO 2025-07-23 02:11:29,752 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 143
INFO 2025-07-23 02:11:29,761 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:11:29,762 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:11:29,764 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:12:35,322 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:12:35,329 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:12:35,359 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:12:35,360 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 144
INFO 2025-07-23 02:12:35,362 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 145
INFO 2025-07-23 02:12:35,371 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:12:35,372 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:13:07,576 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:13:07,592 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:13:07,639 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 146
INFO 2025-07-23 02:13:07,650 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:13:07,656 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:13:07,683 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 147
INFO 2025-07-23 02:13:07,692 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:14:18,622 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:17:04,457 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:17:20,890 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:17:50,343 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:18:38,394 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:18:54,839 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:19:08,666 basehttp 1672680 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-23 02:19:29,488 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:19:29,581 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:19:29,605 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:19:29,642 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 148
INFO 2025-07-23 02:19:29,663 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:19:29,675 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 149
INFO 2025-07-23 02:19:29,686 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:20:04,702 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:20:04,706 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:20:04,713 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:20:04,732 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 150
INFO 2025-07-23 02:20:04,733 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 151
INFO 2025-07-23 02:20:04,742 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:20:04,743 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:22:42,084 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:22:42,116 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:22:42,151 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 152
INFO 2025-07-23 02:22:42,162 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:22:42,197 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:22:42,216 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 153
INFO 2025-07-23 02:22:42,226 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:23:56,817 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:23:56,832 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 154
INFO 2025-07-23 02:23:56,833 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:23:56,836 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:23:56,843 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:23:56,909 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 155
INFO 2025-07-23 02:23:56,920 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:24:31,425 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:31,425 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:31,454 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:31,476 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 156
INFO 2025-07-23 02:24:31,487 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:24:31,488 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 157
INFO 2025-07-23 02:24:31,498 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:24:52,774 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:52,822 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:52,851 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:24:52,864 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 158
INFO 2025-07-23 02:24:52,874 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:24:52,884 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 159
INFO 2025-07-23 02:24:52,894 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:09,365 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:09,367 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:09,386 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 160
INFO 2025-07-23 02:25:09,398 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:09,407 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:09,593 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 161
INFO 2025-07-23 02:25:09,602 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:26,527 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:26,533 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:26,553 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 162
INFO 2025-07-23 02:25:26,561 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:26,563 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:26,601 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 163
INFO 2025-07-23 02:25:26,611 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:39,392 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:39,422 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:39,445 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:39,452 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 164
INFO 2025-07-23 02:25:39,462 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:39,682 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 165
INFO 2025-07-23 02:25:39,692 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:52,445 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:52,448 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:52,487 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:25:52,513 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 166
INFO 2025-07-23 02:25:52,515 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 167
INFO 2025-07-23 02:25:52,523 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:25:52,524 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:26:05,786 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:05,786 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:05,817 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 168
INFO 2025-07-23 02:26:05,819 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:05,831 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:26:06,016 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 169
INFO 2025-07-23 02:26:06,026 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:26:18,599 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:18,608 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:18,647 basehttp 1672680 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-23 02:26:18,657 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 170
INFO 2025-07-23 02:26:18,667 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:26:18,852 rate_limiter 1672680 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 171
INFO 2025-07-23 02:26:18,862 basehttp 1672680 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-23 02:26:32,828 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 172
INFO 2025-07-23 02:26:32,836 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 173
INFO 2025-07-23 02:26:59,026 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 174
INFO 2025-07-23 02:26:59,130 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 175
INFO 2025-07-23 02:27:11,430 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 176
INFO 2025-07-23 02:27:11,470 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 177
INFO 2025-07-23 02:27:26,212 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 178
INFO 2025-07-23 02:27:26,241 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 179
INFO 2025-07-23 02:28:02,213 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 180
INFO 2025-07-23 02:28:02,417 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 181
INFO 2025-07-23 02:28:39,068 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 182
INFO 2025-07-23 02:28:39,308 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 183
INFO 2025-07-23 02:29:08,602 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 184
INFO 2025-07-23 02:29:08,808 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 185
INFO 2025-07-23 02:41:58,278 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 186
INFO 2025-07-23 02:41:58,348 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 187
INFO 2025-07-23 02:42:14,643 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 188
INFO 2025-07-23 02:42:14,871 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 189
INFO 2025-07-23 02:42:47,470 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 190
INFO 2025-07-23 02:42:47,681 rate_limiter 1690464 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 191
ERROR 2025-07-23 07:15:28,862 exception 1690464 140209767298624 Invalid HTTP_HOST header: 'faust-could-appreciate-the-issue-i-have.mil.gg'. You may need to add 'faust-could-appreciate-the-issue-i-have.mil.gg' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'faust-could-appreciate-the-issue-i-have.mil.gg'. You may need to add 'faust-could-appreciate-the-issue-i-have.mil.gg' to ALLOWED_HOSTS.
