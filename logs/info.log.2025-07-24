ERROR 2025-07-24 11:33:43,410 middleware 2095462 *************** =================== 全局异常捕获 ===================
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 235, in _get_response_async
    callback, callback_args, callback_kwargs = self.resolve_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 663, in resolve
    for pattern in self.url_patterns:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 715, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 72, in <module>
    path("health-wuyun-liuqi/", health_wuyun_liuqi_api.urls),
NameError: name 'health_wuyun_liuqi_api' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 461, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 277, in __call__
    return call_result.result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 451, in result
    return self.__get_result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 353, in main_wrap
    result = await self.awaitable(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/corsheaders/middleware.py", line 56, in __call__
    result = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django_ratelimit/middleware.py", line 12, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 732, in __call__
    response = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']
ERROR 2025-07-24 11:33:43,416 middleware 2095462 *************** =================== 全局异常捕获 ===================
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 235, in _get_response_async
    callback, callback_args, callback_kwargs = self.resolve_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 663, in resolve
    for pattern in self.url_patterns:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 715, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 461, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 277, in __call__
    return call_result.result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 451, in result
    return self.__get_result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 353, in main_wrap
    result = await self.awaitable(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/corsheaders/middleware.py", line 56, in __call__
    result = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django_ratelimit/middleware.py", line 12, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 732, in __call__
    response = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 58, in <module>
    path("bank/", bank_api.urls),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 420, in urls
    self._validate()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/main.py", line 539, in _validate
    raise ConfigError(msg.strip())
ninja.errors.ConfigError: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']
ERROR 2025-07-24 11:33:43,428 middleware 2095462 *************** 路径: /api/auth/refresh-token
ERROR 2025-07-24 11:33:43,441 middleware 2095462 *************** 路径: /api/auth/refresh-token
ERROR 2025-07-24 11:33:43,442 middleware 2095462 *************** 方法: POST
ERROR 2025-07-24 11:33:43,442 middleware 2095462 *************** 方法: POST
ERROR 2025-07-24 11:33:43,442 middleware 2095462 *************** 错误类型: ConfigError
ERROR 2025-07-24 11:33:43,442 middleware 2095462 *************** 错误类型: ConfigError
ERROR 2025-07-24 11:33:43,442 middleware 2095462 *************** 错误信息: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']
ERROR 2025-07-24 11:33:43,443 middleware 2095462 *************** 详细堆栈已记录在上一条 exception 日志中
ERROR 2025-07-24 11:33:43,443 middleware 2095462 *************** ====================================================
ERROR 2025-07-24 11:33:43,444 middleware 2095462 *************** 错误信息: Looks like you created multiple NinjaAPIs or TestClients
To let ninja distinguish them you need to set either unique version or urls_namespace
 - NinjaAPI(..., version='2.0.0')
 - NinjaAPI(..., urls_namespace='otherapi')
Already registered: ['bank_api', 'doubao_aichat_api', 'invite_api', 'questionnaire_api', 'db_health_monitor_api', 'auth_api', 'async_bank_api', 'routertest1_api', 'routertest2_api', 'routertest3_api']
ERROR 2025-07-24 11:33:43,445 middleware 2095462 *************** 详细堆栈已记录在上一条 exception 日志中
ERROR 2025-07-24 11:33:43,445 middleware 2095462 *************** ====================================================
INFO 2025-07-24 11:35:19,774 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-07-24 11:35:19,972 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
INFO 2025-07-24 11:35:20,410 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 3
INFO 2025-07-24 11:35:22,386 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 4
INFO 2025-07-24 11:35:22,403 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 5
INFO 2025-07-24 11:35:26,781 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 6
INFO 2025-07-24 11:35:27,106 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 7
WARNING 2025-07-24 11:35:28,200 base 2095884 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-24 11:35:28,248 base 2095884 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-24 11:39:29,295 ark_chat_consumer 2095884 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-24 11:39:34,057 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 8
INFO 2025-07-24 11:39:39,958 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 9
INFO 2025-07-24 11:39:42,213 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 10
INFO 2025-07-24 11:39:42,798 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 11
WARNING 2025-07-24 11:39:43,264 base 2095884 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-24 11:39:43,311 base 2095884 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-24 11:51:25,346 ark_chat_consumer 2095884 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-24 15:14:03,805 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 12
INFO 2025-07-24 15:14:03,876 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 13
INFO 2025-07-24 15:14:04,272 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 14
INFO 2025-07-24 15:14:07,293 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 15
INFO 2025-07-24 15:14:10,379 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 16
INFO 2025-07-24 15:14:10,432 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 17
WARNING 2025-07-24 15:14:10,823 base 2095884 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-24 15:14:10,871 base 2095884 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-24 15:14:45,175 ark_chat_consumer 2095884 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-24 22:18:25,890 rate_limiter 2095884 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 18
