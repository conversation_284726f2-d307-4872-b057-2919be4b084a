INFO 2025-07-21 00:10:22,816 manager 1070724 139862236971008 已重新加载所有提示词
ERROR 2025-07-21 01:18:25,211 middleware 1085231 140281841169984 =================== 全局异常捕获 ===================
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 235, in _get_response_async
    callback, callback_args, callback_kwargs = self.resolve_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 663, in resolve
    for pattern in self.url_patterns:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 715, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 150, in __acall__
    response = response or await self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 461, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 277, in __call__
    return call_result.result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 451, in result
    return self.__get_result()
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 353, in main_wrap
    result = await self.awaitable(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 44, in inner
    response = await sync_to_async(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 479, in __call__
    ret: _R = await loop.run_in_executor(
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/asgiref/sync.py", line 536, in thread_handler
    return func(*args, **kwargs)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/corsheaders/middleware.py", line 56, in __call__
    result = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django_ratelimit/middleware.py", line 12, in __call__
    return self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", line 732, in __call__
    response = self.get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 57, in inner
    response = response_for_exception(request, exc)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 140, in response_for_exception
    response = handle_uncaught_exception(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 184, in handle_uncaught_exception
    callback = resolver.resolve_error_handler(500)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 729, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", line 26, in <module>
    re_path(r'^api/', include("api.urls")),
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/urls.py", line 13, in <module>
    from .ninja_apis import (
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", line 17, in <module>
    from .bazi_analysis_api import bazi_analysis_router
  File "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", line 136, in <module>
    def get_prompt_content(request, category: str, version: str = "v1") -> JsonResponse:
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 268, in decorator
    self.add_api_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/router.py", line 319, in add_api_operation
    path_view.add_operation(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 426, in add_operation
    operation = OperationClass(
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/operation.py", line 82, in __init__
    self.signature = ViewSignature(self.path, self.view_func)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 75, in __init__
    func_param = self._get_param_type(name, arg)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/ninja/signature/details.py", line 255, in _get_param_type
    default == self.signature.empty
AssertionError: 'version' is a path param, default not allowed
ERROR 2025-07-21 01:18:25,220 middleware 1085231 140281841169984 路径: /
ERROR 2025-07-21 01:18:25,220 middleware 1085231 140281841169984 方法: GET
ERROR 2025-07-21 01:18:25,220 middleware 1085231 140281841169984 错误类型: AssertionError
ERROR 2025-07-21 01:18:25,221 middleware 1085231 140281841169984 错误信息: 'version' is a path param, default not allowed
ERROR 2025-07-21 01:18:25,221 middleware 1085231 140281841169984 详细堆栈已记录在上一条 exception 日志中
ERROR 2025-07-21 01:18:25,221 middleware 1085231 140281841169984 ====================================================
INFO 2025-07-21 01:51:26,905 autoreload 1093951 139643634192384 Watching for file changes with StatReloader
INFO 2025-07-21 01:52:05,317 rate_limiter 1094096 140260363665408 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 135
INFO 2025-07-21 02:04:49,785 rate_limiter 1097741 139730649567232 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 136
INFO 2025-07-21 02:04:49,804 rate_limiter 1097741 139730649567232 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 137
INFO 2025-07-21 02:04:49,817 rate_limiter 1097741 139730649567232 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 138
INFO 2025-07-21 02:04:50,297 rate_limiter 1097741 139730649567232 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 139
INFO 2025-07-21 02:06:19,118 rate_limiter 1097741 139730649567232 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 140
