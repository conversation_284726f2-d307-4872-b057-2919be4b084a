INFO 2025-07-20 01:35:03,589 rate_limiter 188605 140221199302656 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-07-20 01:35:06,365 rate_limiter 188605 140221199302656 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
INFO 2025-07-20 12:29:40,935 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-07-20 12:29:41,112 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
INFO 2025-07-20 12:29:41,335 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 3
INFO 2025-07-20 12:30:03,913 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 4
INFO 2025-07-20 12:30:03,931 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 5
INFO 2025-07-20 12:30:04,316 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 6
INFO 2025-07-20 12:31:05,663 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 7
INFO 2025-07-20 12:31:05,678 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 8
INFO 2025-07-20 12:31:06,065 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 9
INFO 2025-07-20 12:34:20,371 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 10
INFO 2025-07-20 12:34:20,426 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 11
INFO 2025-07-20 12:34:21,820 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 12
INFO 2025-07-20 12:34:45,841 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 13
INFO 2025-07-20 12:34:45,859 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 14
INFO 2025-07-20 12:34:46,350 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 15
INFO 2025-07-20 12:35:15,742 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 16
INFO 2025-07-20 12:35:15,781 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 17
INFO 2025-07-20 12:35:16,520 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 18
INFO 2025-07-20 12:35:50,887 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 19
INFO 2025-07-20 12:35:51,115 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 20
INFO 2025-07-20 12:35:51,794 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 21
INFO 2025-07-20 12:39:49,642 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:user_questionnaires_api 调用次数: 1
INFO 2025-07-20 12:39:49,743 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 22
INFO 2025-07-20 12:39:49,789 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 23
INFO 2025-07-20 12:39:49,813 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:get_user_info_api 调用次数: 1
INFO 2025-07-20 12:39:51,677 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 24
INFO 2025-07-20 12:39:52,684 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 25
INFO 2025-07-20 12:39:52,702 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 26
WARNING 2025-07-20 12:39:53,116 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:39:53,163 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 12:42:44,008 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:42:47,099 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 27
INFO 2025-07-20 12:42:47,236 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 28
WARNING 2025-07-20 12:42:47,614 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:42:47,661 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:43:16,065 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:43:19,150 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 29
INFO 2025-07-20 12:43:19,161 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 30
WARNING 2025-07-20 12:43:19,517 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:43:19,563 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:43:21,870 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:43:24,946 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 31
INFO 2025-07-20 12:43:24,997 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 32
WARNING 2025-07-20 12:43:25,398 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:43:25,444 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:43:44,791 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:43:47,933 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 33
INFO 2025-07-20 12:43:47,985 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 34
WARNING 2025-07-20 12:43:48,273 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:43:48,319 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:44:09,987 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:44:13,136 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 35
INFO 2025-07-20 12:44:13,364 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 36
WARNING 2025-07-20 12:44:13,824 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:44:13,870 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:44:30,604 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:44:33,754 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 37
INFO 2025-07-20 12:44:33,799 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 38
WARNING 2025-07-20 12:44:34,138 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:44:34,185 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 12:44:48,084 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:44:51,237 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 39
INFO 2025-07-20 12:44:51,241 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 40
WARNING 2025-07-20 12:44:51,586 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:44:51,632 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:45:19,329 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:45:22,325 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 41
INFO 2025-07-20 12:45:22,367 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 42
WARNING 2025-07-20 12:45:22,734 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:45:22,780 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:45:31,493 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:45:34,572 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 43
INFO 2025-07-20 12:45:34,593 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 44
WARNING 2025-07-20 12:45:34,964 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:45:35,010 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:46:01,605 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:46:04,570 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 45
INFO 2025-07-20 12:46:04,600 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 46
WARNING 2025-07-20 12:46:05,031 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:46:05,077 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:46:25,875 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:46:28,788 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 47
INFO 2025-07-20 12:46:28,833 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 48
WARNING 2025-07-20 12:46:29,399 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:46:29,445 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 12:46:43,614 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 12:46:46,619 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 49
INFO 2025-07-20 12:46:46,654 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 50
WARNING 2025-07-20 12:46:47,261 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 12:46:47,307 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:03:43,686 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:03:47,433 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 51
WARNING 2025-07-20 13:03:47,900 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:03:47,946 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:03:50,362 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 52
INFO 2025-07-20 13:04:00,390 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:04:20,562 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 53
INFO 2025-07-20 13:04:20,951 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 54
WARNING 2025-07-20 13:04:21,589 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:04:21,636 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:04:29,421 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:04:32,426 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 55
INFO 2025-07-20 13:04:32,603 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 56
WARNING 2025-07-20 13:04:34,251 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:04:34,297 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:04:47,830 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:04:50,777 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 57
INFO 2025-07-20 13:04:51,052 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 58
WARNING 2025-07-20 13:04:51,310 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:04:51,356 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:05:06,404 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:05:09,570 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 59
INFO 2025-07-20 13:05:09,633 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 60
WARNING 2025-07-20 13:05:11,097 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:05:11,144 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:05:24,802 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:05:27,839 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 61
INFO 2025-07-20 13:05:27,962 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 62
WARNING 2025-07-20 13:05:29,094 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:05:29,140 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:05:44,589 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:05:47,709 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 63
INFO 2025-07-20 13:05:48,110 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 64
WARNING 2025-07-20 13:05:48,498 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:05:48,544 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:06:05,055 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:06:08,351 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 65
WARNING 2025-07-20 13:06:08,970 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:06:09,016 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:06:10,507 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 66
INFO 2025-07-20 13:06:37,542 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:06:40,682 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 67
INFO 2025-07-20 13:06:41,085 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 68
WARNING 2025-07-20 13:06:42,043 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:06:42,089 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:08:37,670 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 13:08:41,215 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 69
INFO 2025-07-20 13:08:42,100 rate_limiter 918881 139901553938432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 70
WARNING 2025-07-20 13:08:43,368 base 918881 139901553938432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 13:08:43,414 base 918881 139901553938432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 13:30:12,182 ark_chat_consumer 918881 139901553938432 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-20 13:36:48,907 autoreload 935130 140072567754752 Watching for file changes with StatReloader
INFO 2025-07-20 13:37:31,849 autoreload 935130 140072567754752 /home/<USER>/riyue-llm/myproject/demo_api/demo_api/settings.py changed, reloading.
INFO 2025-07-20 13:37:33,140 autoreload 935459 139899300937728 Watching for file changes with StatReloader
INFO 2025-07-20 13:37:59,539 autoreload 935625 140204210843648 Watching for file changes with StatReloader
INFO 2025-07-20 14:15:54,903 rate_limiter 943323 140609869993536 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 71
INFO 2025-07-20 14:17:46,691 rate_limiter 943323 140610474341952 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 72
INFO 2025-07-20 14:17:47,659 rate_limiter 943323 140609869993536 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 73
INFO 2025-07-20 14:17:47,794 rate_limiter 943323 140609869993536 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 74
INFO 2025-07-20 14:18:40,067 rate_limiter 943323 140609645966912 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 75
INFO 2025-07-20 14:18:40,754 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 76
INFO 2025-07-20 14:18:40,785 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 77
WARNING 2025-07-20 14:18:41,151 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:18:41,199 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-20 14:20:38,348 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:20:41,350 rate_limiter 943323 140609645966912 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 78
INFO 2025-07-20 14:20:41,598 rate_limiter 943323 140609645966912 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 79
WARNING 2025-07-20 14:20:41,987 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:20:42,034 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 14:20:52,746 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:20:55,514 rate_limiter 943323 140609645966912 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 80
INFO 2025-07-20 14:20:55,545 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 81
WARNING 2025-07-20 14:20:55,950 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:20:55,997 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 14:21:19,888 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:21:22,673 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 82
INFO 2025-07-20 14:21:22,887 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 83
WARNING 2025-07-20 14:21:23,306 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:21:23,353 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:21:36,711 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:21:39,554 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 84
INFO 2025-07-20 14:21:39,768 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 85
WARNING 2025-07-20 14:21:40,548 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:21:40,594 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:22:05,196 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:22:07,997 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 86
INFO 2025-07-20 14:22:08,063 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 87
WARNING 2025-07-20 14:22:08,652 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:22:08,698 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:22:11,660 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:22:14,026 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 88
INFO 2025-07-20 14:22:14,063 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 89
WARNING 2025-07-20 14:22:14,478 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:22:14,524 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:22:30,112 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:22:32,848 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 90
INFO 2025-07-20 14:22:32,896 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 91
WARNING 2025-07-20 14:22:34,513 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:22:34,560 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:23:01,608 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:23:04,402 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 92
INFO 2025-07-20 14:23:04,954 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 93
WARNING 2025-07-20 14:23:05,966 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:23:06,014 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-20 14:23:39,756 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:23:42,680 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 94
INFO 2025-07-20 14:23:42,718 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 95
WARNING 2025-07-20 14:23:43,108 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:23:43,154 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:23:48,921 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:23:51,897 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 96
INFO 2025-07-20 14:23:51,978 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 97
WARNING 2025-07-20 14:23:52,627 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:23:52,673 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:24:21,023 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:25:18,482 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 98
INFO 2025-07-20 14:25:18,730 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 99
WARNING 2025-07-20 14:25:19,191 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:25:19,237 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 14:26:39,753 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-20 14:30:33,230 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 100
INFO 2025-07-20 14:31:58,834 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 101
INFO 2025-07-20 14:31:59,037 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 102
WARNING 2025-07-20 14:31:59,386 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:31:59,432 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:32:10,707 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:32:43,137 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 103
INFO 2025-07-20 14:32:43,181 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 104
WARNING 2025-07-20 14:32:43,512 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:32:43,559 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:32:58,569 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:33:01,419 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 105
INFO 2025-07-20 14:33:01,478 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 106
WARNING 2025-07-20 14:33:01,952 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:33:01,998 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:33:14,732 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:33:17,741 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 107
INFO 2025-07-20 14:33:17,794 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 108
WARNING 2025-07-20 14:33:18,270 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:33:18,316 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:33:35,634 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:33:38,497 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 109
INFO 2025-07-20 14:33:38,543 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 110
WARNING 2025-07-20 14:33:39,141 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:33:39,187 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:33:49,387 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:33:52,217 rate_limiter 943323 140609637574208 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 111
INFO 2025-07-20 14:33:52,272 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 112
WARNING 2025-07-20 14:33:52,623 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:33:52,670 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:34:26,053 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-20 14:34:28,919 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 113
INFO 2025-07-20 14:34:28,967 rate_limiter 943323 140609612396096 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 114
WARNING 2025-07-20 14:34:30,307 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 14:34:30,354 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-20 14:35:08,274 ark_chat_consumer 943323 140610930909184 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-20 14:40:41,370 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 115
INFO 2025-07-20 14:40:41,422 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 116
INFO 2025-07-20 14:40:41,574 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 117
INFO 2025-07-20 14:40:56,865 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 118
INFO 2025-07-20 14:40:56,880 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 119
INFO 2025-07-20 14:40:57,354 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 120
INFO 2025-07-20 14:41:12,441 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 121
INFO 2025-07-20 14:41:12,743 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 122
INFO 2025-07-20 14:41:12,997 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 123
INFO 2025-07-20 14:41:45,880 rate_limiter 943323 140609629181504 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 124
INFO 2025-07-20 14:41:46,076 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 125
INFO 2025-07-20 14:41:46,314 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 126
INFO 2025-07-20 15:06:15,297 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 127
INFO 2025-07-20 15:06:16,187 rate_limiter 943323 140609620788800 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 128
WARNING 2025-07-20 15:06:17,680 base 943323 140610930909184 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 15:06:17,726 base 943323 140610930909184 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-20 15:30:44,205 rate_limiter 960438 139821536237120 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 129
INFO 2025-07-20 15:57:22,379 rate_limiter 967265 139939249382976 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 130
INFO 2025-07-20 15:57:24,167 rate_limiter 967265 139939258824256 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 131
INFO 2025-07-20 15:57:26,229 rate_limiter 967265 139939258824256 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 132
INFO 2025-07-20 15:57:27,514 rate_limiter 967265 139938638120512 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 133
WARNING 2025-07-20 15:57:28,721 base 967265 139939715768320 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 15:57:28,775 base 967265 139939715768320 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.054秒
WARNING 2025-07-20 15:57:31,573 base 967265 139939715768320 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-20 15:57:31,621 base 967265 139939715768320 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-20 15:57:31,810 rate_limiter 967265 139939715768320 [RATE_LIMIT] 用户1(每周) API:chat_api 调用次数: 1
INFO 2025-07-20 15:57:56,280 base 967265 139939715768320 ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 24.489秒
INFO 2025-07-20 15:59:34,451 ark_chat_consumer 967265 139939715768320 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-20 15:59:34,453 bazi_advice_consumer 967265 139939715768320 [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-20 16:04:04,282 rate_limiter 969115 140264081450560 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 134
