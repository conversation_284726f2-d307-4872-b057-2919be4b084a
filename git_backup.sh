#!/bin/bash

# 颜色配置
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 固定的备份目录名称
BACKUP_DIR="../demo_api_backup_20241222"

# 显示脚本信息
echo -e "${YELLOW}=========================================${NC}"
echo -e "${YELLOW}       Django 项目自动 Git 备份脚本      ${NC}"
echo -e "${YELLOW}=========================================${NC}"
echo -e "当前时间: $(date "+%Y-%m-%d %H:%M:%S")"
echo -e "备份目录: ${BACKUP_DIR}"
echo -e "----------------------------------------"

# 检查是否在正确的目录
if [ ! -f "manage.py" ]; then
    echo -e "${RED}错误: 当前目录不是Django项目根目录${NC}"
    echo -e "请在含有manage.py的项目根目录下运行此脚本"
    exit 1
fi

# 询问是否继续
read -p "是否继续备份项目? (Y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 1. 同步文件到备份目录（排除.git和日志）
echo -e "${YELLOW}[步骤 1]${NC} 同步文件到备份目录..."
mkdir -p "${BACKUP_DIR}"
rsync -av --progress ./ "${BACKUP_DIR}/" \
--exclude='.git' \
--exclude='*.log'

# 2. 进入备份目录
echo -e "${YELLOW}[步骤 2]${NC} 进入备份目录..."
cd "${BACKUP_DIR}" || { echo -e "${RED}无法进入备份目录${NC}"; exit 1; }

# 检查是否是Git仓库，如果不是则初始化
if [ ! -d ".git" ]; then
    echo -e "${YELLOW}初始化Git仓库...${NC}"
    git init
    
    # 检查远程仓库是否存在
    if ! git remote | grep -q "origin"; then
        echo -e "${YELLOW}未找到远程仓库，请设置...${NC}"
        read -p "请输入Git远程仓库URL: " REPO_URL
        git remote add origin "${REPO_URL}"
    fi
fi

# 3. 添加所有更改
echo -e "${YELLOW}[步骤 3]${NC} 添加所有更改..."
git add .

# 4. 提交更改
echo -e "${YELLOW}[步骤 4]${NC} 提交更改..."
COMMIT_MESSAGE="2025-07-30 凌晨 八字系统+高并发优化"
git commit -m "${COMMIT_MESSAGE}"

# 如果没有要提交的内容，脚本将在这里退出
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}没有需要提交的更改，或提交失败。${NC}"
    exit 1
fi

# 5. 推送到远程仓库 jiu9ye 小写的1
echo -e "${YELLOW}[步骤 5]${NC} 推送到远程仓库..."
git push origin master

echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}备份完成!${NC}"
echo -e "备份目录: ${BACKUP_DIR}"
echo -e "提交信息: ${COMMIT_MESSAGE}"
echo -e "${GREEN}=========================================${NC}" 